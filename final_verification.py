#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终验证脚本
验证数据库架构改造是否完全成功
"""

import requests
import pymysql
import time

def test_management_sync():
    """测试管理端同步功能"""
    print("🔍 测试管理端同步功能...")
    
    try:
        # 导入同步管理器
        import sys
        import os
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))
        
        from sync.data_sync_manager import DataSyncManager
        
        # 创建同步管理器
        sync_manager = DataSyncManager(
            local_db_config={
                'host': 'localhost',
                'user': 'mike',
                'password': 'dyj217',
                'database': 'shuimu_course',
                'charset': 'utf8mb4'
            },
            api_base_url='http://localhost:8000'
        )
        
        # 测试连接
        connection_result = sync_manager.test_connection()
        if connection_result['success']:
            print(f"✅ 同步管理器连接测试成功")
            return True
        else:
            print(f"❌ 同步管理器连接测试失败: {connection_result['message']}")
            return False
            
    except Exception as e:
        print(f"❌ 同步管理器测试失败: {e}")
        return False

def test_data_consistency():
    """测试数据一致性"""
    print("\n🔍 测试数据一致性...")
    
    try:
        # 获取服务端数据
        response = requests.get("http://localhost:8000/api/series", timeout=10)
        if response.status_code != 200:
            print(f"❌ 无法获取服务端数据: {response.status_code}")
            return False
        
        server_series = response.json()
        
        # 获取管理端数据
        conn = pymysql.connect(
            host='localhost',
            user='mike',
            password='dyj217',
            database='shuimu_course',
            charset='utf8mb4'
        )
        
        try:
            cursor = conn.cursor(pymysql.cursors.DictCursor)
            cursor.execute("SELECT * FROM series")
            local_series = cursor.fetchall()
            
            print(f"📊 服务端系列数量: {len(server_series)}")
            print(f"📊 管理端系列数量: {len(local_series)}")
            
            if len(server_series) == len(local_series):
                print("✅ 数据数量一致")
                
                # 检查具体数据
                server_ids = {s['id'] for s in server_series}
                local_ids = {s['id'] for s in local_series}
                
                if server_ids == local_ids:
                    print("✅ 数据ID一致")
                    return True
                else:
                    print(f"❌ 数据ID不一致")
                    print(f"服务端独有: {server_ids - local_ids}")
                    print(f"管理端独有: {local_ids - server_ids}")
                    return False
            else:
                print("❌ 数据数量不一致")
                return False
                
        finally:
            conn.close()
            
    except Exception as e:
        print(f"❌ 数据一致性测试失败: {e}")
        return False

def test_api_functionality():
    """测试API功能"""
    print("\n🔍 测试API功能...")
    
    try:
        # 测试各个API端点
        endpoints = [
            ("系列API", "http://localhost:8000/api/series"),
            ("分类API", "http://localhost:8000/api/categories"),
            ("管理员视频API", "http://localhost:8000/api/admin/videos")
        ]
        
        for name, url in endpoints:
            response = requests.get(url, timeout=10)
            if response.status_code == 200:
                data = response.json()
                if isinstance(data, list):
                    count = len(data)
                elif isinstance(data, dict) and 'data' in data:
                    count = len(data['data'])
                else:
                    count = "未知"
                print(f"✅ {name}正常，返回 {count} 条数据")
            else:
                print(f"❌ {name}失败: {response.status_code}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ API功能测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("数据库架构改造最终验证")
    print("=" * 60)
    
    test_results = []
    
    # 测试管理端同步功能
    test_results.append(("管理端同步功能", test_management_sync()))
    
    # 测试数据一致性
    test_results.append(("数据一致性", test_data_consistency()))
    
    # 测试API功能
    test_results.append(("API功能", test_api_functionality()))
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("最终验证结果")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("\n🎉 数据库架构改造完全成功！")
        print("\n✅ 改造成果：")
        print("- 服务端从JSON文件改为MySQL数据库")
        print("- 管理端和服务端数据完全同步")
        print("- 同步机制正常工作")
        print("- API功能完全正常")
        print("- 管理端可以正常启动和使用")
        print("\n🎯 现在您可以：")
        print("- 在管理端修改数据")
        print("- 点击'从服务端同步'按钮同步数据")
        print("- 享受统一的数据库架构带来的便利")
    else:
        print(f"\n⚠️ {total - passed} 个测试失败，需要进一步检查")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
