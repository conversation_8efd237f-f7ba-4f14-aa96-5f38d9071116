#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据源切换功能
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_data_source_switch():
    """测试数据源切换功能"""
    print("🔍 测试数据源切换功能...")
    
    try:
        from PyQt6.QtWidgets import QApplication
        from ui.main_window import MainWindow
        from utils.config import Config
        from config.api_config import api_config
        import time
        
        # 创建应用
        app = QApplication(sys.argv)
        
        # 创建主窗口
        config = Config()
        main_window = MainWindow(config)
        main_window.show()
        
        # 等待初始化完成
        time.sleep(3)
        
        print("\n📊 初始状态检查:")
        print(f"   当前数据源: {main_window.current_data_source}")
        print(f"   API配置: {api_config.get_mode_name()}")
        
        # 检查数据源工具栏是否存在
        if hasattr(main_window, 'data_source_combo'):
            print("✅ 数据源切换工具栏已创建")
            
            # 检查下拉框选项
            combo = main_window.data_source_combo
            print(f"   下拉框选项数量: {combo.count()}")
            for i in range(combo.count()):
                text = combo.itemText(i)
                data = combo.itemData(i)
                print(f"   选项 {i}: {text} (值: {data})")
            
            print(f"   当前选择: {combo.currentText()}")
            
            # 测试切换到本地数据库
            print("\n🔄 测试切换到本地数据库...")
            combo.setCurrentIndex(1)  # 切换到本地数据库
            time.sleep(2)  # 等待切换完成
            
            print(f"   切换后数据源: {main_window.current_data_source}")
            print(f"   切换后API配置: {api_config.get_mode_name()}")
            
            # 测试切换回服务端
            print("\n🔄 测试切换回服务端...")
            combo.setCurrentIndex(0)  # 切换回服务端
            time.sleep(2)  # 等待切换完成
            
            print(f"   切换后数据源: {main_window.current_data_source}")
            print(f"   切换后API配置: {api_config.get_mode_name()}")
            
            # 测试刷新按钮
            print("\n🔄 测试刷新功能...")
            main_window.refresh_data_source()
            time.sleep(2)  # 等待刷新完成
            
            print("✅ 数据源切换功能测试完成")
            return True
        else:
            print("❌ 数据源切换工具栏未创建")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        try:
            app.quit()
        except:
            pass

def test_api_config():
    """测试API配置功能"""
    print("\n🔍 测试API配置功能...")
    
    try:
        from config.api_config import api_config
        
        print(f"初始配置: {api_config.get_config()}")
        print(f"初始模式: {api_config.get_mode_name()}")
        
        # 测试模式切换
        print("\n测试模式切换:")
        api_config.set_api_mode(False)
        print(f"切换到本地模式: {api_config.get_mode_name()}")
        
        api_config.set_api_mode(True)
        print(f"切换到API模式: {api_config.get_mode_name()}")
        
        print("✅ API配置功能正常")
        return True
        
    except Exception as e:
        print(f"❌ API配置测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("数据源切换功能测试")
    print("=" * 60)
    
    test_results = []
    
    # 测试API配置
    test_results.append(("API配置功能", test_api_config()))
    
    # 测试数据源切换
    test_results.append(("数据源切换功能", test_data_source_switch()))
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    
    for test_name, result in test_results:
        status = "✅ 正常" if result else "❌ 异常"
        print(f"{test_name}: {status}")
    
    failed_tests = [name for name, result in test_results if not result]
    if failed_tests:
        print(f"\n⚠️ 需要修复: {', '.join(failed_tests)}")
    else:
        print("\n🎉 所有功能都正常！")
        print("\n✅ 功能特点：")
        print("- 🌐 服务端数据：从API获取数据")
        print("- 💾 本地数据库：从本地MySQL获取数据")
        print("- 🔄 一键切换：下拉框选择数据源")
        print("- 🔄 刷新功能：重新加载当前数据源")
        print("- 📊 状态显示：实时显示当前数据源状态")
        print("\n🎊 数据源切换功能完全正常！")

if __name__ == "__main__":
    main()
