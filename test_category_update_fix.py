#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分类更新功能修正验证测试
验证分类更新功能能否正确处理字符串ID和API模式
"""

import sys
import os

# 添加src目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from services.course_service import CourseService
from cache.global_data_manager import global_data_manager
from database.models import DatabaseManager

def test_category_update_fix():
    """测试分类更新修正"""
    print("🧪 测试分类更新修正...")
    
    # 1. 加载数据到内存
    print("\n📦 步骤1: 加载数据到内存...")
    success = global_data_manager.load_all_data_once()
    if not success:
        print("❌ 数据加载失败，无法继续测试")
        return False
    
    # 2. 获取第一个分类进行测试
    print("\n📂 步骤2: 获取测试分类...")
    db_manager = DatabaseManager("sqlite:///test.db")
    course_service = CourseService(db_manager)
    
    category_list_result = global_data_manager.get_category_list(page=1, page_size=1)
    if not category_list_result['success'] or not category_list_result['data']:
        print("❌ 获取分类列表失败")
        return False
    
    first_category = category_list_result['data'][0]
    category_id = first_category['id']
    category_title = first_category['title']
    print(f"测试分类: ID={category_id}, 标题={category_title}")
    
    # 3. 测试分类更新
    print(f"\n🔧 步骤3: 测试分类更新...")
    
    # 准备更新数据
    update_data = {
        'title': f"{category_title} (已更新)",
        'description': '这是一个测试更新',
        'price': 99.99,
        'order_index': 1
    }
    
    try:
        result = course_service.update_category(category_id, update_data)
        
        if result['success']:
            print(f"✅ 分类更新成功: {result['message']}")
            print(f"📊 更新结果: {result.get('data', {})}")
            print(f"🔄 同步状态: {result.get('sync_status', 'unknown')}")
            return True
        else:
            print(f"❌ 分类更新失败: {result['message']}")
            return False
            
    except Exception as e:
        print(f"❌ 分类更新异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_category_id_types():
    """测试不同类型的分类ID处理"""
    print("\n🔍 测试不同类型的分类ID处理...")
    
    db_manager = DatabaseManager("sqlite:///test.db")
    course_service = CourseService(db_manager)
    
    # 获取分类列表
    category_list_result = global_data_manager.get_category_list(page=1, page_size=3)
    if not category_list_result['success']:
        print("❌ 获取分类列表失败")
        return False
    
    categories = category_list_result['data']
    print(f"找到 {len(categories)} 个分类进行ID类型测试")
    
    success_count = 0
    
    for category in categories:
        category_id = category['id']
        category_title = category['title']
        
        print(f"\n测试分类: {category_id} ({category_title})")
        print(f"ID类型: {type(category_id)}")
        
        # 测试字符串ID
        try:
            update_data = {'title': f"{category_title} (字符串ID测试)"}
            result = course_service.update_category(str(category_id), update_data)
            
            if result['success']:
                print(f"✅ 字符串ID更新成功")
                success_count += 1
            else:
                print(f"❌ 字符串ID更新失败: {result['message']}")
        except Exception as e:
            print(f"❌ 字符串ID更新异常: {e}")
    
    print(f"\n📊 ID类型测试结果: {success_count}/{len(categories)} 个分类更新成功")
    return success_count == len(categories)

def test_memory_vs_database_check():
    """测试内存数据与数据库检查的逻辑"""
    print("\n🧠 测试内存数据与数据库检查逻辑...")
    
    db_manager = DatabaseManager("sqlite:///test.db")
    course_service = CourseService(db_manager)
    
    # 测试存在的分类
    category_list_result = global_data_manager.get_category_list(page=1, page_size=1)
    if category_list_result['success'] and category_list_result['data']:
        existing_category = category_list_result['data'][0]
        category_id = existing_category['id']
        
        print(f"测试存在的分类: {category_id}")
        update_data = {'title': f"{existing_category['title']} (存在性测试)"}
        
        result = course_service.update_category(category_id, update_data)
        if result['success']:
            print(f"✅ 存在分类更新成功")
        else:
            print(f"❌ 存在分类更新失败: {result['message']}")
    
    # 测试不存在的分类
    print(f"\n测试不存在的分类: non-existent-category")
    update_data = {'title': '不存在的分类'}
    
    result = course_service.update_category('non-existent-category', update_data)
    if not result['success'] and '分类不存在' in result['message']:
        print(f"✅ 不存在分类正确返回错误: {result['message']}")
        return True
    else:
        print(f"❌ 不存在分类处理异常: {result}")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("分类更新功能修正验证测试")
    print("=" * 60)
    
    try:
        # 测试分类更新修正
        update_success = test_category_update_fix()
        
        if update_success:
            # 测试ID类型处理
            id_type_success = test_category_id_types()
            
            # 测试存在性检查
            existence_success = test_memory_vs_database_check()
            
            if id_type_success and existence_success:
                print("\n" + "=" * 60)
                print("🎉 所有测试通过！分类更新功能已修正。")
                print("现在分类编辑保存应该不会再提示'分类不存在'错误了。")
                print("=" * 60)
            else:
                print("\n⚠️ 部分测试失败，可能存在其他问题")
        else:
            print("\n❌ 分类更新修正测试失败")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
