#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试乐观更新功能
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_optimistic_update():
    """测试乐观更新功能"""
    print("🔍 测试乐观更新功能...")
    
    try:
        from PyQt6.QtWidgets import QApplication
        from ui.main_window import MainWindow
        from utils.config import Config
        
        # 创建应用
        app = QApplication(sys.argv)
        
        # 创建主窗口
        config = Config()
        main_window = MainWindow(config)
        main_window.show()
        
        # 显示课程页面
        main_window.show_course_page()
        
        # 获取课程窗口
        if hasattr(main_window, '_course_widget'):
            course_widget = main_window._course_widget
            
            # 测试立即UI更新方法
            print("📝 测试立即UI更新...")
            
            # 模拟更新数据
            test_series_id = "love-guide-series"
            test_data = {
                'title': '道：恋爱宝典（乐观更新测试）',
                'description': '测试乐观更新功能',
                'price': 99.99
            }
            
            # 调用立即更新方法
            course_widget.update_series_ui_immediately(test_series_id, test_data)
            
            # 测试状态栏消息
            course_widget.show_status_message("✅ 乐观更新测试成功")
            
            print("✅ 乐观更新功能测试完成")
            return True
        else:
            print("❌ 课程窗口创建失败")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        try:
            app.quit()
        except:
            pass

def main():
    """主测试函数"""
    print("=" * 60)
    print("乐观更新功能测试")
    print("=" * 60)
    
    success = test_optimistic_update()
    
    print("\n" + "=" * 60)
    print("测试结果")
    print("=" * 60)
    
    if success:
        print("✅ 乐观更新功能测试成功")
        print("\n🎉 功能特点：")
        print("- ⚡ 立即更新UI显示")
        print("- 💾 更新内存缓存")
        print("- 🔄 更新筛选器")
        print("- 📱 状态栏反馈")
        print("- 🔙 失败时回滚")
    else:
        print("❌ 乐观更新功能测试失败")

if __name__ == "__main__":
    main()
