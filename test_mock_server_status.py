#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Mock Server状态
"""

import requests
import json

def test_server_connection():
    """测试服务器连接"""
    base_url = "http://localhost:8000"
    
    print("🔍 测试Mock Server连接状态...")
    
    try:
        # 测试根路径
        print("\n1. 测试根路径...")
        response = requests.get(f"{base_url}/", timeout=5)
        print(f"   状态码: {response.status_code}")
        print(f"   响应: {response.text[:200]}")
        
        # 测试分类API
        print("\n2. 测试分类API...")
        response = requests.get(f"{base_url}/api/categories", timeout=5)
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   分类数量: {len(data)}")
            if data:
                print(f"   第一个分类: {data[0]}")
        else:
            print(f"   错误响应: {response.text}")
        
        # 测试管理端分类创建API
        print("\n3. 测试管理端分类创建API...")
        test_data = {
            'title': 'API测试分类',
            'seriesId': 'love-guide-series',
            'price': 99.99,
            'description': '通过API创建的测试分类',
            'order_index': 1,
            'isFree': False
        }
        
        response = requests.post(
            f"{base_url}/api/admin/categories",
            json=test_data,
            headers={'Content-Type': 'application/json'},
            timeout=5
        )
        print(f"   状态码: {response.status_code}")
        print(f"   响应: {response.text}")
        
        # 再次获取分类，看是否增加了
        print("\n4. 验证分类是否增加...")
        response = requests.get(f"{base_url}/api/categories", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"   分类数量: {len(data)}")
        
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败：Mock Server未运行")
        print("\n🔧 启动Mock Server的可能方法：")
        print("1. cd mock_server && python -m uvicorn src.main:app --reload --host 0.0.0.0 --port 8000")
        print("2. cd ../mock_server && python -m uvicorn src.main:app --reload --host 0.0.0.0 --port 8000")
        print("3. 检查是否有其他启动脚本")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def analyze_api_endpoints():
    """分析API端点"""
    print("\n🔍 分析API端点配置...")
    
    # 从代码中提取的API端点
    endpoints = {
        "分类相关": [
            "GET /api/categories - 获取分类列表",
            "POST /api/admin/categories - 创建分类",
            "PUT /api/categories/{id} - 更新分类",
            "DELETE /api/categories/{id} - 删除分类"
        ],
        "系列相关": [
            "GET /api/series - 获取系列列表", 
            "POST /api/series - 创建系列",
            "PUT /api/series/{id} - 更新系列",
            "DELETE /api/series/{id} - 删除系列"
        ],
        "视频相关": [
            "GET /api/videos - 获取视频列表",
            "POST /api/videos - 创建视频",
            "PUT /api/videos/{id} - 更新视频",
            "DELETE /api/videos/{id} - 删除视频"
        ]
    }
    
    for category, endpoint_list in endpoints.items():
        print(f"\n{category}:")
        for endpoint in endpoint_list:
            print(f"   {endpoint}")

def main():
    """主函数"""
    print("=" * 60)
    print("Mock Server状态检查")
    print("=" * 60)
    
    # 分析API端点
    analyze_api_endpoints()
    
    # 测试服务器连接
    server_running = test_server_connection()
    
    print("\n" + "=" * 60)
    print("检查结果")
    print("=" * 60)
    
    if server_running:
        print("✅ Mock Server运行正常")
        print("\n🎯 下一步：检查数据持久化机制")
        print("- 重启Mock Server后数据是否保留")
        print("- 数据是否保存到数据库或文件")
    else:
        print("❌ Mock Server未运行")
        print("\n🔧 解决方案：")
        print("1. 找到Mock Server项目目录")
        print("2. 启动Mock Server")
        print("3. 验证API端点")
        print("4. 检查数据持久化")

if __name__ == "__main__":
    main()
