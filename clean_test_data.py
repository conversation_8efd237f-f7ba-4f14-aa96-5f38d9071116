#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清理测试数据
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def clean_test_data():
    """清理测试数据"""
    print("🧹 清理测试数据...")
    
    try:
        from database.models import DatabaseManager, Series
        from utils.config import Config
        
        # 创建数据库连接
        config = Config()
        db_config = config.get_database_config()
        connection_string = f"mysql+pymysql://{db_config['user']}:{db_config['password']}@{db_config['host']}:{db_config['port']}/{db_config['database']}?charset={db_config['charset']}"
        db_manager = DatabaseManager(connection_string)
        
        session = db_manager.get_session()
        
        # 查找测试数据
        test_series = session.query(Series).filter(
            Series.title.like('%测试%') | Series.title.like('%test%')
        ).all()
        
        print(f"🗑️ 找到 {len(test_series)} 个测试系列:")
        for series in test_series:
            print(f"   - {series.title} (ID: {series.id})")
        
        if test_series:
            # 直接删除，不询问
            for series in test_series:
                session.delete(series)
            session.commit()
            print(f"✅ 已删除 {len(test_series)} 个测试系列")
        else:
            print("✅ 没有找到测试数据")
        
        # 检查清理后的数据
        remaining_series = session.query(Series).all()
        print(f"\n📊 清理后剩余系列数量: {len(remaining_series)}")
        for series in remaining_series:
            print(f"   - {series.title} (ID: {series.id})")
        
        session.close()
        
    except Exception as e:
        print(f"❌ 清理测试数据失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    clean_test_data()
