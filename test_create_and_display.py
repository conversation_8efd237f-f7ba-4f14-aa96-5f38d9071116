#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试创建系列并立即显示
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_create_and_display():
    """测试创建系列并立即显示"""
    print("🔍 测试创建系列并立即显示...")
    
    try:
        from PyQt6.QtWidgets import QApplication
        from ui.main_window import MainWindow
        from utils.config import Config
        import time
        
        # 创建应用
        app = QApplication(sys.argv)
        
        # 创建主窗口
        config = Config()
        main_window = MainWindow(config)
        main_window.show()
        
        # 显示课程页面
        main_window.show_course_page()
        time.sleep(2)  # 等待初始化完成
        
        # 切换到系列管理标签页
        if hasattr(main_window, '_course_widget'):
            course_widget = main_window._course_widget
            course_widget.tab_widget.setCurrentIndex(1)  # 系列管理是第1个标签页
            time.sleep(1)
            
            print("📊 创建前的系列数量检查...")
            
            # 检查创建前的缓存数据
            before_count = 0
            if hasattr(course_widget, '_cached_series_data'):
                before_count = len(course_widget._cached_series_data)
                print(f"📦 本地缓存系列数量: {before_count}")

            # 检查全局缓存数据
            global_before_count = 0
            try:
                from cache.global_data_manager import global_data_manager
                global_before_count = len(global_data_manager._series_data)
                print(f"🌐 全局缓存系列数量: {global_before_count}")
            except:
                pass
            
            # 检查表格行数
            table_before_count = course_widget.series_table.rowCount()
            print(f"📋 表格行数: {table_before_count}")
            
            print("\n🆕 模拟创建新系列...")
            
            # 模拟创建新系列的数据
            test_series_data = {
                'id': 'test-series-12345',
                'title': '测试系列：立即显示验证',
                'description': '这是一个测试系列，用于验证立即显示功能',
                'price': 88.88,
                'is_published': False
            }
            
            # 模拟添加到缓存
            if hasattr(course_widget, '_cached_series_data'):
                course_widget._cached_series_data.append(test_series_data)
                print(f"💾 已添加到本地缓存")
            
            # 添加到全局缓存
            try:
                global_data_manager.add_series_to_memory(test_series_data)
            except:
                pass
            
            # 立即添加到表格
            course_widget.add_series_to_table_immediately(test_series_data)
            
            # 更新筛选器
            course_widget.update_series_filters_after_create(test_series_data)
            
            print("\n📊 创建后的数量检查...")
            
            # 检查创建后的缓存数据
            after_count = 0
            if hasattr(course_widget, '_cached_series_data'):
                after_count = len(course_widget._cached_series_data)
                print(f"📦 本地缓存系列数量: {before_count} -> {after_count}")

            # 检查全局缓存数据
            global_after_count = global_before_count
            try:
                global_after_count = len(global_data_manager._series_data)
                print(f"🌐 全局缓存系列数量: {global_before_count} -> {global_after_count}")
            except:
                pass
            
            # 检查表格行数
            table_after_count = course_widget.series_table.rowCount()
            print(f"📋 表格行数: {table_before_count} -> {table_after_count}")
            
            # 验证结果
            success = (
                after_count == before_count + 1 and
                global_after_count == global_before_count + 1 and
                table_after_count == table_before_count + 1
            )
            
            if success:
                print("\n✅ 创建并立即显示测试成功！")
                print("- 本地缓存已更新")
                print("- 全局缓存已更新")
                print("- 表格已立即显示新行")
                return True
            else:
                print("\n❌ 创建并立即显示测试失败")
                return False
        else:
            print("❌ 课程窗口创建失败")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        try:
            app.quit()
        except:
            pass

def main():
    """主测试函数"""
    print("=" * 60)
    print("创建系列并立即显示测试")
    print("=" * 60)
    
    success = test_create_and_display()
    
    print("\n" + "=" * 60)
    print("测试结果")
    print("=" * 60)
    
    if success:
        print("🎉 创建并立即显示功能正常！")
        print("\n✅ 修复成果：")
        print("- 💾 本地缓存立即更新")
        print("- 🌐 全局缓存立即更新")
        print("- 📋 表格立即显示新行")
        print("- 🔄 筛选器立即更新")
        print("\n现在创建新系列后会立即显示在界面上！")
    else:
        print("❌ 创建并立即显示功能仍有问题")

if __name__ == "__main__":
    main()
