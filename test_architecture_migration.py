#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库架构改造验证测试
验证服务端MySQL、管理端同步等功能
"""

import requests
import mysql.connector
import json
import time

def test_server_database():
    """测试服务端数据库"""
    print("🔍 测试服务端数据库...")
    
    try:
        # 连接服务端数据库
        conn = mysql.connector.connect(
            host='localhost',
            user='shuimu_server',
            password='dyj217',
            database='shuimu_course_server',
            charset='utf8mb4'
        )
        
        cursor = conn.cursor(dictionary=True)
        
        # 检查表结构
        cursor.execute("SHOW TABLES")
        tables = cursor.fetchall()
        table_names = [table['Tables_in_shuimu_course_server'] for table in tables]
        
        expected_tables = ['users', 'series', 'categories', 'videos', 'orders', 'user_purchases', 'user_video_progress']
        missing_tables = [table for table in expected_tables if table not in table_names]
        
        if missing_tables:
            print(f"❌ 缺少表: {missing_tables}")
            return False
        else:
            print(f"✅ 所有表都存在: {table_names}")
        
        # 检查数据
        cursor.execute("SELECT COUNT(*) as count FROM series")
        series_count = cursor.fetchone()['count']
        
        cursor.execute("SELECT COUNT(*) as count FROM categories")
        category_count = cursor.fetchone()['count']
        
        cursor.execute("SELECT COUNT(*) as count FROM videos")
        video_count = cursor.fetchone()['count']
        
        print(f"📊 数据统计: 系列={series_count}, 分类={category_count}, 视频={video_count}")
        
        if series_count > 0 and category_count > 0 and video_count > 0:
            print("✅ 服务端数据库测试通过")
            return True
        else:
            print("❌ 服务端数据库数据不完整")
            return False
            
    except Exception as e:
        print(f"❌ 服务端数据库测试失败: {e}")
        return False
    finally:
        if 'conn' in locals():
            conn.close()

def test_server_api():
    """测试服务端API"""
    print("\n🔍 测试服务端API...")
    
    try:
        # 测试根路径
        response = requests.get("http://localhost:8000/", timeout=5)
        if response.status_code != 200:
            print(f"❌ 服务端根路径访问失败: {response.status_code}")
            return False
        
        # 测试系列API
        response = requests.get("http://localhost:8000/api/series", timeout=10)
        if response.status_code != 200:
            print(f"❌ 系列API访问失败: {response.status_code}")
            return False
        
        series_data = response.json()
        print(f"✅ 系列API正常，返回 {len(series_data)} 个系列")
        
        # 测试分类API
        response = requests.get("http://localhost:8000/api/categories", timeout=10)
        if response.status_code != 200:
            print(f"❌ 分类API访问失败: {response.status_code}")
            return False
        
        categories_data = response.json()
        print(f"✅ 分类API正常，返回 {len(categories_data)} 个分类")
        
        # 测试视频API
        response = requests.get("http://localhost:8000/api/videos", timeout=10)
        if response.status_code != 200:
            print(f"❌ 视频API访问失败: {response.status_code}")
            return False
        
        videos_data = response.json()
        print(f"✅ 视频API正常，返回 {len(videos_data)} 个视频")
        
        print("✅ 服务端API测试通过")
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务端，请确保服务端正在运行")
        return False
    except Exception as e:
        print(f"❌ 服务端API测试失败: {e}")
        return False

def test_api_update():
    """测试API更新功能"""
    print("\n🔍 测试API更新功能...")
    
    try:
        # 获取第一个系列
        response = requests.get("http://localhost:8000/api/series", timeout=10)
        if response.status_code != 200:
            print("❌ 无法获取系列列表")
            return False
        
        series_list = response.json()
        if not series_list:
            print("❌ 系列列表为空")
            return False
        
        first_series = series_list[0]
        series_id = first_series['id']
        original_title = first_series['title']
        
        print(f"📝 测试更新系列: {series_id} - {original_title}")
        
        # 更新标题
        test_title = f"{original_title} (测试更新)"
        update_data = {"title": test_title}
        
        response = requests.put(
            f"http://localhost:8000/api/series/{series_id}",
            json=update_data,
            timeout=10
        )
        
        if response.status_code != 200:
            print(f"❌ 更新API失败: {response.status_code}")
            return False
        
        result = response.json()
        if result.get('success'):
            print(f"✅ API更新成功: {result.get('message')}")
            
            # 验证更新是否生效
            time.sleep(1)  # 等待一秒
            response = requests.get("http://localhost:8000/api/series", timeout=10)
            updated_series_list = response.json()
            
            updated_series = next((s for s in updated_series_list if s['id'] == series_id), None)
            if updated_series and updated_series['title'] == test_title:
                print(f"✅ 更新验证成功: {updated_series['title']}")
                
                # 恢复原标题
                restore_data = {"title": original_title}
                requests.put(
                    f"http://localhost:8000/api/series/{series_id}",
                    json=restore_data,
                    timeout=10
                )
                print(f"🔄 已恢复原标题")
                
                return True
            else:
                print(f"❌ 更新验证失败")
                return False
        else:
            print(f"❌ API更新失败: {result.get('message')}")
            return False
            
    except Exception as e:
        print(f"❌ API更新测试失败: {e}")
        return False

def test_sync_manager():
    """测试同步管理器"""
    print("\n🔍 测试同步管理器...")
    
    try:
        # 导入同步管理器
        import sys
        import os
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))
        
        from sync.data_sync_manager import DataSyncManager
        
        # 创建同步管理器
        sync_manager = DataSyncManager(
            local_db_config={
                'host': 'localhost',
                'user': 'mike',
                'password': 'dyj217',
                'database': 'shuimu_course',
                'charset': 'utf8mb4'
            },
            api_base_url='http://localhost:8000'
        )
        
        # 测试连接
        connection_result = sync_manager.test_connection()
        if not connection_result['success']:
            print(f"❌ 同步管理器连接测试失败: {connection_result['message']}")
            return False
        
        print(f"✅ 同步管理器连接测试成功")
        
        # 测试从服务端同步
        sync_result = sync_manager.sync_from_server()
        if sync_result['success']:
            print(f"✅ 从服务端同步成功: {sync_result['message']}")
            return True
        else:
            print(f"❌ 从服务端同步失败: {sync_result['message']}")
            return False
            
    except Exception as e:
        print(f"❌ 同步管理器测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("数据库架构改造验证测试")
    print("=" * 60)
    
    test_results = []
    
    # 测试服务端数据库
    test_results.append(("服务端数据库", test_server_database()))
    
    # 测试服务端API
    test_results.append(("服务端API", test_server_api()))
    
    # 测试API更新
    test_results.append(("API更新功能", test_api_update()))
    
    # 测试同步管理器
    test_results.append(("同步管理器", test_sync_manager()))
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("\n🎉 所有测试通过！数据库架构改造成功！")
        print("\n✅ 改造完成项目：")
        print("- 服务端从JSON文件改为MySQL数据库")
        print("- 管理端和服务端使用统一数据库结构")
        print("- 数据迁移成功完成")
        print("- API功能正常工作")
        print("- 同步机制正常运行")
    else:
        print(f"\n⚠️ {total - passed} 个测试失败，请检查相关功能")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
