#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试MySQL管理器
"""

import sys
import os

# 添加mock_server路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../mock_server/src'))

try:
    from database.mysql_manager import mysql_manager
    print("✅ MySQL管理器导入成功")
    
    # 测试连接
    videos = mysql_manager.get_all_videos()
    print(f"✅ 获取视频成功: {len(videos)} 个视频")
    
    if videos:
        print(f"第一个视频: {videos[0]}")
    
except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()
