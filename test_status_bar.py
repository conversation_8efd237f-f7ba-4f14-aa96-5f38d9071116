#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试状态栏消息显示
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_status_bar_message():
    """测试状态栏消息"""
    print("🔍 测试状态栏消息...")
    
    try:
        from PyQt6.QtWidgets import QApplication
        from ui.main_window import MainWindow
        from utils.config import Config
        
        # 创建应用
        app = QApplication(sys.argv)
        
        # 创建主窗口
        config = Config()
        main_window = MainWindow(config)
        main_window.show()
        
        # 显示课程页面
        main_window.show_course_page()
        
        # 获取课程窗口
        if hasattr(main_window, '_course_widget'):
            course_widget = main_window._course_widget
            
            # 测试状态消息
            print("📝 测试状态消息...")
            course_widget.show_status_message("✅ 测试状态消息显示")
            
            # 检查状态栏
            if hasattr(main_window, 'status_bar'):
                current_message = main_window.status_bar.currentMessage()
                print(f"📱 状态栏当前消息: {current_message}")
                
                if "测试状态消息显示" in current_message:
                    print("✅ 状态栏消息显示正常")
                    return True
                else:
                    print("❌ 状态栏消息显示异常")
                    return False
            else:
                print("❌ 主窗口没有状态栏")
                return False
        else:
            print("❌ 课程窗口创建失败")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        try:
            app.quit()
        except:
            pass

if __name__ == "__main__":
    success = test_status_bar_message()
    print(f"\n测试结果: {'✅ 成功' if success else '❌ 失败'}")
