#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试更新功能
模拟管理端的更新操作
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_course_service_update():
    """测试课程服务的更新功能"""
    print("🔍 测试课程服务更新功能...")
    
    try:
        from database.models import DatabaseManager
        from services.course_service import CourseService
        from utils.config import Config
        
        # 创建数据库连接
        config = Config()
        db_config = config.get_database_config()
        connection_string = f"mysql+pymysql://{db_config['user']}:{db_config['password']}@{db_config['host']}:{db_config['port']}/{db_config['database']}?charset={db_config['charset']}"
        db_manager = DatabaseManager(connection_string)
        
        # 创建课程服务
        course_service = CourseService(db_manager, use_api=False, use_cache=True)
        
        # 获取系列列表
        series_result = course_service.get_series_list(page=1, page_size=10)
        if not series_result.get('success'):
            print(f"❌ 获取系列列表失败: {series_result}")
            return False
        
        series_list = series_result['data']
        print(f"📊 获取到 {len(series_list)} 个系列")
        
        # 找到道系列
        dao_series = None
        for series in series_list:
            if "道" in series.get('title', ''):
                dao_series = series
                break
        
        if not dao_series:
            print("❌ 没有找到道系列")
            return False
        
        print(f"🎯 找到道系列: {dao_series['id']} - {dao_series['title']}")
        
        # 测试更新
        original_title = dao_series['title']
        test_title = f"{original_title}（功能测试更新）"
        
        print(f"🔄 尝试更新标题: {original_title} -> {test_title}")
        
        # 执行更新
        update_result = course_service.update_series(dao_series['id'], {
            'title': test_title,
            'description': dao_series.get('description', ''),
            'price': dao_series.get('price', 0)
        })
        
        if update_result.get('success'):
            print(f"✅ 更新成功: {update_result.get('message')}")
            
            # 验证更新是否生效
            import time
            time.sleep(2)  # 等待异步同步完成
            
            # 重新获取系列列表
            verify_result = course_service.get_series_list(page=1, page_size=10)
            if verify_result.get('success'):
                verify_series_list = verify_result['data']
                
                for series in verify_series_list:
                    if series['id'] == dao_series['id']:
                        if series['title'] == test_title:
                            print(f"✅ 更新验证成功: {series['title']}")
                            
                            # 恢复原标题
                            restore_result = course_service.update_series(dao_series['id'], {
                                'title': original_title,
                                'description': dao_series.get('description', ''),
                                'price': dao_series.get('price', 0)
                            })
                            
                            if restore_result.get('success'):
                                print(f"🔄 已恢复原标题: {original_title}")
                                return True
                            else:
                                print(f"⚠️ 恢复原标题失败: {restore_result}")
                                return True  # 更新功能本身是成功的
                        else:
                            print(f"❌ 更新验证失败，标题仍为: {series['title']}")
                            return False
                        break
                
                print("❌ 验证时没有找到对应系列")
                return False
            else:
                print(f"❌ 验证时获取系列列表失败: {verify_result}")
                return False
        else:
            print(f"❌ 更新失败: {update_result}")
            return False
            
    except Exception as e:
        print(f"❌ 课程服务更新测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_server_api_directly():
    """直接测试服务端API"""
    print("\n🔍 直接测试服务端API...")
    
    try:
        import requests
        
        # 获取系列列表
        response = requests.get("http://localhost:8000/api/series", timeout=10)
        if response.status_code != 200:
            print(f"❌ 获取服务端系列失败: {response.status_code}")
            return False
        
        series_list = response.json()
        print(f"📊 服务端有 {len(series_list)} 个系列")
        
        # 找到道系列
        dao_series = None
        for series in series_list:
            if "道" in series.get('title', ''):
                dao_series = series
                break
        
        if not dao_series:
            print("❌ 服务端没有找到道系列")
            return False
        
        print(f"🎯 服务端找到道系列: {dao_series['id']} - {dao_series['title']}")
        
        # 测试更新API
        original_title = dao_series['title']
        test_title = f"{original_title}（API直接测试）"
        
        print(f"🔄 直接调用API更新: {original_title} -> {test_title}")
        
        update_data = {'title': test_title}
        response = requests.put(
            f"http://localhost:8000/api/series/{dao_series['id']}",
            json=update_data,
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"✅ API更新成功: {result.get('message')}")
                
                # 验证更新
                verify_response = requests.get("http://localhost:8000/api/series", timeout=10)
                if verify_response.status_code == 200:
                    verify_series_list = verify_response.json()
                    
                    for series in verify_series_list:
                        if series['id'] == dao_series['id']:
                            if series['title'] == test_title:
                                print(f"✅ API更新验证成功: {series['title']}")
                                
                                # 恢复原标题
                                restore_data = {'title': original_title}
                                restore_response = requests.put(
                                    f"http://localhost:8000/api/series/{dao_series['id']}",
                                    json=restore_data,
                                    timeout=10
                                )
                                
                                if restore_response.status_code == 200:
                                    print(f"🔄 API已恢复原标题: {original_title}")
                                
                                return True
                            else:
                                print(f"❌ API更新验证失败，标题仍为: {series['title']}")
                                return False
                            break
                    
                    print("❌ API验证时没有找到对应系列")
                    return False
                else:
                    print(f"❌ API验证时获取系列失败: {verify_response.status_code}")
                    return False
            else:
                print(f"❌ API更新失败: {result}")
                return False
        else:
            print(f"❌ API调用失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 服务端API测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("测试更新功能")
    print("=" * 60)
    
    test_results = []
    
    # 测试课程服务更新
    test_results.append(("课程服务更新", test_course_service_update()))
    
    # 测试服务端API
    test_results.append(("服务端API", test_server_api_directly()))
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("测试结果")
    print("=" * 60)
    
    for test_name, result in test_results:
        status = "✅ 正常" if result else "❌ 异常"
        print(f"{test_name}: {status}")
    
    failed_tests = [name for name, result in test_results if not result]
    if failed_tests:
        print(f"\n⚠️ 发现问题: {', '.join(failed_tests)}")
    else:
        print("\n🎉 所有更新功能都正常工作！")
        print("现在管理端的更新操作应该可以正常保存到本地数据库并同步到服务端了。")

if __name__ == "__main__":
    main()
