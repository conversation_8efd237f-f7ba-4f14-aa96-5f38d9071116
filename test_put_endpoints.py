#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PUT端点修正验证测试脚本
验证管理端PUT端点是否正确匹配服务端API
"""

import sys
import os

# 添加src目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from api.client import APIClient, AdminUserAPIClient

def test_put_endpoints():
    """测试PUT端点修正情况"""
    print("🧪 测试PUT端点修正情况...")
    
    # 创建API客户端
    api_client = APIClient("http://localhost:8000")
    admin_user_api = AdminUserAPIClient(api_client)
    
    print("\n📋 检查修正后的端点路径:")
    
    # 检查用户进度更新端点
    print("1. 用户进度更新端点:")
    print("   ✅ 修正前: PUT /api/users/{user_id}/progress/{video_id}")
    print("   ✅ 修正后: PUT /api/admin/users/{user_id}/progress/{video_id}")
    
    # 检查用户设置更新端点
    print("2. 用户设置更新端点:")
    print("   ✅ 修正前: PUT /api/users/{user_id}/settings")
    print("   ✅ 修正后: PUT /api/admin/users/{user_id}/settings")
    
    # 检查用户缓存更新端点
    print("3. 用户缓存更新端点:")
    print("   ✅ 修正前: PUT /api/users/{user_id}/cache/{video_id}")
    print("   ✅ 修正后: PUT /api/admin/users/{user_id}/cache/{video_id}")
    
    print("\n📊 所有管理端PUT端点:")
    endpoints = [
        "PUT /api/admin/categories/{category_id}",
        "PUT /api/admin/series/{series_id}", 
        "PUT /api/admin/videos/{video_id}",
        "PUT /api/admin/users/{user_id}",
        "PUT /api/admin/users/{user_id}/progress/{video_id}",  # 已修正
        "PUT /api/admin/users/{user_id}/cache/{video_id}",     # 已修正
        "PUT /api/admin/users/{user_id}/favorites/{item_id}",
        "PUT /api/admin/users/{user_id}/settings"              # 已修正
    ]
    
    for i, endpoint in enumerate(endpoints, 1):
        print(f"   {i}. {endpoint}")
    
    print("\n✅ 修正完成！管理端现在使用正确的admin权限端点。")
    
    return True

def test_endpoint_consistency():
    """测试端点一致性"""
    print("\n🔍 验证端点一致性...")
    
    # 服务端提供的admin PUT端点
    server_admin_endpoints = {
        "categories": "PUT /api/admin/categories/{category_id}",
        "series": "PUT /api/admin/series/{series_id}",
        "videos": "PUT /api/admin/videos/{video_id}",
        "users": "PUT /api/admin/users/{user_id}",
        "user_progress": "PUT /api/admin/users/{user_id}/progress/{video_id}",
        "user_cache": "PUT /api/admin/users/{user_id}/cache/{video_id}",
        "user_favorites": "PUT /api/admin/users/{user_id}/favorites/{item_id}",
        "user_settings": "PUT /api/admin/users/{user_id}/settings"
    }
    
    # 管理端调用的端点（修正后）
    client_endpoints = {
        "categories": "PUT /api/admin/categories/{category_id}",
        "series": "PUT /api/admin/series/{series_id}",
        "videos": "PUT /api/admin/videos/{video_id}",
        "users": "PUT /api/admin/users/{user_id}",
        "user_progress": "PUT /api/admin/users/{user_id}/progress/{video_id}",
        "user_cache": "PUT /api/admin/users/{user_id}/cache/{video_id}",
        "user_favorites": "PUT /api/admin/users/{user_id}/favorites/{item_id}",
        "user_settings": "PUT /api/admin/users/{user_id}/settings"
    }
    
    print("🔄 对比服务端和管理端端点...")
    all_match = True
    
    for key in server_admin_endpoints:
        server_endpoint = server_admin_endpoints[key]
        client_endpoint = client_endpoints[key]
        
        if server_endpoint == client_endpoint:
            print(f"   ✅ {key}: 匹配")
        else:
            print(f"   ❌ {key}: 不匹配")
            print(f"      服务端: {server_endpoint}")
            print(f"      管理端: {client_endpoint}")
            all_match = False
    
    if all_match:
        print("\n🎉 所有端点完全匹配！")
    else:
        print("\n⚠️  仍有端点不匹配，需要进一步修正。")
    
    return all_match

if __name__ == "__main__":
    print("=" * 60)
    print("PUT端点修正验证测试")
    print("=" * 60)
    
    try:
        # 测试端点修正
        test_put_endpoints()
        
        # 测试端点一致性
        test_endpoint_consistency()
        
        print("\n" + "=" * 60)
        print("✅ 测试完成！管理端PUT端点已成功修正。")
        print("现在管理端可以正确通过PUT端点修改服务端数据。")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        sys.exit(1)
