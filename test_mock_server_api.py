#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Mock Server API
"""

import requests
import json

def test_mock_server_api():
    """测试Mock Server API"""
    base_url = "http://localhost:8000"
    
    print("🔍 测试Mock Server API...")
    
    try:
        # 1. 获取当前分类数量
        print("\n1. 获取当前分类数量...")
        response = requests.get(f"{base_url}/api/categories", timeout=5)
        if response.status_code == 200:
            categories_before = response.json()
            print(f"   创建前分类数量: {len(categories_before)}")
        else:
            print(f"   获取分类失败: {response.status_code}")
            return False
        
        # 2. 测试创建分类API
        print("\n2. 测试创建分类API...")
        test_data = {
            'title': 'API测试分类',
            'seriesId': 'love-guide-series',
            'price': 88.88,
            'description': '通过API创建的测试分类',
            'order_index': 1,
            'isFree': False
        }
        
        response = requests.post(
            f"{base_url}/api/admin/categories",
            json=test_data,
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        
        print(f"   状态码: {response.status_code}")
        print(f"   响应内容: {response.text}")
        
        if response.status_code in [200, 201]:
            print("   ✅ 分类创建成功")
            created_category = response.json()
            print(f"   创建的分类ID: {created_category.get('id', 'N/A')}")
        else:
            print("   ❌ 分类创建失败")
            return False
        
        # 3. 再次获取分类，验证是否增加
        print("\n3. 验证分类是否增加...")
        response = requests.get(f"{base_url}/api/categories", timeout=5)
        if response.status_code == 200:
            categories_after = response.json()
            print(f"   创建后分类数量: {len(categories_after)}")
            
            if len(categories_after) > len(categories_before):
                print("   ✅ 分类数量增加，API创建成功")
                
                # 查找新创建的分类
                new_categories = [c for c in categories_after if c['title'] == 'API测试分类']
                if new_categories:
                    print(f"   新分类详情: {new_categories[0]}")
                
                return True
            else:
                print("   ❌ 分类数量未增加，可能是内存存储")
                return False
        else:
            print(f"   获取分类失败: {response.status_code}")
            return False
        
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败：Mock Server未运行")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_data_persistence():
    """测试数据持久化"""
    print("\n🔍 测试数据持久化...")
    
    # 这里可以添加重启Mock Server后的测试
    # 但由于我们无法直接控制Mock Server，先跳过
    print("   需要手动重启Mock Server来测试数据持久化")

def analyze_problem():
    """分析问题"""
    print("\n🔍 问题分析...")
    
    print("根据之前的测试结果：")
    print("1. ✅ Mock Server正在运行 (http://localhost:8000)")
    print("2. ✅ 获取分类API正常 (返回11个分类)")
    print("3. ❓ 创建分类API需要验证")
    print("4. ❓ 数据持久化机制需要验证")
    
    print("\n可能的问题：")
    print("1. Mock Server使用内存存储，重启后数据丢失")
    print("2. 创建分类API有问题，无法正确保存")
    print("3. 客户端发送的数据格式与服务端期望不匹配")
    print("4. 缺少跨数据源同步机制")

def main():
    """主函数"""
    print("=" * 60)
    print("Mock Server API测试")
    print("=" * 60)
    
    # 测试API
    api_success = test_mock_server_api()
    
    # 测试数据持久化
    test_data_persistence()
    
    # 分析问题
    analyze_problem()
    
    print("\n" + "=" * 60)
    print("测试结果")
    print("=" * 60)
    
    if api_success:
        print("✅ Mock Server API测试成功")
        print("\n🎯 下一步：")
        print("1. 验证数据持久化机制")
        print("2. 实现跨数据源同步")
        print("3. 完善错误处理")
    else:
        print("❌ Mock Server API测试失败")
        print("\n🔧 需要检查：")
        print("1. Mock Server实现")
        print("2. API端点配置")
        print("3. 数据格式匹配")

if __name__ == "__main__":
    main()
