#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查数据库完整性和垃圾数据
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def check_database_integrity():
    """检查数据库完整性"""
    print("🔍 检查数据库完整性...")
    
    try:
        from database.models import DatabaseManager
        from utils.config import Config
        
        # 创建数据库连接
        config = Config()
        db_config = config.get_database_config()
        connection_string = f"mysql+pymysql://{db_config['user']}:{db_config['password']}@{db_config['host']}:{db_config['port']}/{db_config['database']}?charset={db_config['charset']}"
        db_manager = DatabaseManager(connection_string)
        
        session = db_manager.get_session()
        
        # 检查系列数据
        print("\n📚 检查系列数据...")
        from database.models import Series
        series_list = session.query(Series).all()
        
        print(f"📊 本地数据库系列总数: {len(series_list)}")
        
        # 显示所有系列
        for i, series in enumerate(series_list, 1):
            print(f"   {i}. ID: {series.id}")
            print(f"      标题: {series.title}")
            print(f"      价格: ¥{series.price}")
            print(f"      发布状态: {'已发布' if series.is_published else '未发布'}")
            print(f"      创建时间: {series.created_at}")
            print()
        
        # 检查分类数据
        print("📂 检查分类数据...")
        from database.models import Category
        category_list = session.query(Category).all()
        
        print(f"📊 本地数据库分类总数: {len(category_list)}")
        
        # 显示所有分类
        for i, category in enumerate(category_list, 1):
            print(f"   {i}. ID: {category.id}")
            print(f"      标题: {category.title}")
            print(f"      系列ID: {category.series_id}")
            print(f"      价格: ¥{category.price}")
            print(f"      创建时间: {category.created_at}")
            print()
        
        # 检查视频数据
        print("📹 检查视频数据...")
        from database.models import Video
        video_list = session.query(Video).all()
        
        print(f"📊 本地数据库视频总数: {len(video_list)}")
        
        # 检查可能的垃圾数据
        print("\n🗑️ 检查可能的垃圾数据...")
        
        # 检查重复的系列标题
        series_titles = {}
        for series in series_list:
            if series.title in series_titles:
                series_titles[series.title].append(series)
            else:
                series_titles[series.title] = [series]
        
        duplicate_series = {title: series_list for title, series_list in series_titles.items() if len(series_list) > 1}
        
        if duplicate_series:
            print("⚠️ 发现重复的系列标题:")
            for title, series_list in duplicate_series.items():
                print(f"   标题: {title}")
                for series in series_list:
                    print(f"      - ID: {series.id}, 创建时间: {series.created_at}")
        else:
            print("✅ 没有发现重复的系列标题")
        
        # 检查测试数据
        test_series = [s for s in series_list if '测试' in s.title or 'test' in s.title.lower()]
        if test_series:
            print(f"\n🧪 发现测试数据 ({len(test_series)} 个):")
            for series in test_series:
                print(f"   - {series.title} (ID: {series.id})")
        else:
            print("\n✅ 没有发现测试数据")
        
        session.close()
        
        return {
            'series_count': len(series_list),
            'category_count': len(category_list),
            'video_count': len(video_list),
            'duplicate_series': duplicate_series,
            'test_series': test_series
        }
        
    except Exception as e:
        print(f"❌ 检查数据库失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def clean_test_data():
    """清理测试数据"""
    print("\n🧹 清理测试数据...")
    
    try:
        from database.models import DatabaseManager, Series
        from utils.config import Config
        
        # 创建数据库连接
        config = Config()
        db_config = config.get_database_config()
        connection_string = f"mysql+pymysql://{db_config['user']}:{db_config['password']}@{db_config['host']}:{db_config['port']}/{db_config['database']}?charset={db_config['charset']}"
        db_manager = DatabaseManager(connection_string)
        
        session = db_manager.get_session()
        
        # 查找测试数据
        test_series = session.query(Series).filter(
            Series.title.like('%测试%') | Series.title.like('%test%')
        ).all()
        
        if test_series:
            print(f"🗑️ 找到 {len(test_series)} 个测试系列，准备删除:")
            for series in test_series:
                print(f"   - {series.title} (ID: {series.id})")
            
            # 确认删除
            confirm = input("\n确定要删除这些测试数据吗？(y/N): ")
            if confirm.lower() == 'y':
                for series in test_series:
                    session.delete(series)
                session.commit()
                print(f"✅ 已删除 {len(test_series)} 个测试系列")
            else:
                print("❌ 取消删除操作")
        else:
            print("✅ 没有找到测试数据")
        
        session.close()
        
    except Exception as e:
        print(f"❌ 清理测试数据失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("=" * 60)
    print("数据库完整性检查")
    print("=" * 60)
    
    # 检查数据库
    result = check_database_integrity()
    
    if result:
        print("\n" + "=" * 60)
        print("检查结果汇总")
        print("=" * 60)
        print(f"📊 系列数量: {result['series_count']}")
        print(f"📊 分类数量: {result['category_count']}")
        print(f"📊 视频数量: {result['video_count']}")
        
        if result['duplicate_series']:
            print(f"⚠️ 重复系列: {len(result['duplicate_series'])} 组")
        else:
            print("✅ 无重复系列")
        
        if result['test_series']:
            print(f"🧪 测试数据: {len(result['test_series'])} 个")
            
            # 询问是否清理测试数据
            clean_test_data()
        else:
            print("✅ 无测试数据")
    
    print("\n🎯 建议:")
    print("1. 定期检查数据库完整性")
    print("2. 及时清理测试数据")
    print("3. 监控重复数据的产生")

if __name__ == "__main__":
    main()
