#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试优化效果
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_main_window_optimization():
    """测试主窗口优化"""
    print("🔍 测试主窗口优化...")
    
    try:
        from PyQt6.QtWidgets import QApplication
        from ui.main_window import MainWindow
        from utils.config import Config
        
        # 创建应用
        app = QApplication(sys.argv)
        
        # 创建主窗口
        config = Config()
        main_window = MainWindow(config)
        
        print("✅ 主窗口创建成功")
        
        # 测试课程页面单例模式
        print("🔄 第一次显示课程页面...")
        main_window.show_course_page()
        
        print("🔄 第二次显示课程页面...")
        main_window.show_course_page()
        
        print("🔄 第三次显示课程页面...")
        main_window.show_course_page()
        
        # 检查是否只有一个课程窗口实例
        if hasattr(main_window, '_course_widget'):
            print("✅ 课程窗口单例模式正常工作")
            
            # 检查初始化状态
            course_widget = main_window._course_widget
            if hasattr(course_widget, 'is_initialized') and course_widget.is_initialized:
                print("✅ 课程窗口初始化状态正常")
            else:
                print("❌ 课程窗口初始化状态异常")
        else:
            print("❌ 课程窗口单例模式失败")
        
        app.quit()
        return True
        
    except Exception as e:
        print(f"❌ 主窗口优化测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_status_message_system():
    """测试状态消息系统"""
    print("\n🔍 测试状态消息系统...")
    
    try:
        from PyQt6.QtWidgets import QApplication, QMainWindow, QStatusBar
        from ui.course_window import CourseManagementWindow
        from database.models import DatabaseManager
        from utils.config import Config
        
        # 创建应用
        app = QApplication(sys.argv)
        
        # 创建主窗口和状态栏
        main_window = QMainWindow()
        status_bar = QStatusBar()
        main_window.setStatusBar(status_bar)
        main_window.status_bar = status_bar
        
        # 创建数据库管理器
        config = Config()
        db_config = config.get_database_config()
        connection_string = f"mysql+pymysql://{db_config['user']}:{db_config['password']}@{db_config['host']}:{db_config['port']}/{db_config['database']}?charset={db_config['charset']}"
        db_manager = DatabaseManager(connection_string)
        
        # 创建课程窗口
        course_window = CourseManagementWindow(db_manager, main_window)
        
        # 测试状态消息
        print("📝 测试状态消息显示...")
        course_window.show_status_message("✅ 测试消息")
        
        # 检查状态栏是否显示消息
        if status_bar.currentMessage() == "✅ 测试消息":
            print("✅ 状态消息系统正常工作")
        else:
            print(f"❌ 状态消息系统异常，当前消息: {status_bar.currentMessage()}")
        
        app.quit()
        return True
        
    except Exception as e:
        print(f"❌ 状态消息系统测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("测试优化效果")
    print("=" * 60)
    
    test_results = []
    
    # 测试主窗口优化
    test_results.append(("主窗口优化", test_main_window_optimization()))
    
    # 测试状态消息系统
    test_results.append(("状态消息系统", test_status_message_system()))
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("优化测试结果")
    print("=" * 60)
    
    for test_name, result in test_results:
        status = "✅ 正常" if result else "❌ 异常"
        print(f"{test_name}: {status}")
    
    failed_tests = [name for name, result in test_results if not result]
    if failed_tests:
        print(f"\n⚠️ 需要进一步优化: {', '.join(failed_tests)}")
    else:
        print("\n🎉 所有优化都正常工作！")
        print("\n✅ 优化成果：")
        print("- 移除了弹窗提示，改为状态栏显示")
        print("- 实现了窗口单例模式，避免重复创建")
        print("- 修复了重复初始化问题")
        print("- 优化了UI对象生命周期管理")

if __name__ == "__main__":
    main()
