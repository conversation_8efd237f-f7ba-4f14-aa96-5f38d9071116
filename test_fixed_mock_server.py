#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的Mock Server
"""

import requests
import json

def test_fixed_mock_server():
    """测试修复后的Mock Server"""
    base_url = "http://localhost:8001"
    
    print("🔍 测试修复后的Mock Server...")
    
    try:
        # 1. 测试根路径
        print("\n1. 测试根路径...")
        response = requests.get(f"{base_url}/", timeout=5)
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            print("   ✅ Mock Server运行正常")
        
        # 2. 获取当前分类数量
        print("\n2. 获取当前分类数量...")
        response = requests.get(f"{base_url}/api/categories", timeout=5)
        if response.status_code == 200:
            categories_before = response.json()
            print(f"   创建前分类数量: {len(categories_before)}")
        else:
            print(f"   获取分类失败: {response.status_code}")
            return False
        
        # 3. 测试创建分类API（使用MySQL）
        print("\n3. 测试创建分类API（MySQL版本）...")
        test_data = {
            'title': 'MySQL测试分类',
            'seriesId': 'love-guide-series',
            'price': 99.99,
            'description': '通过MySQL API创建的测试分类',
            'order_index': 1,
            'isFree': False
        }
        
        response = requests.post(
            f"{base_url}/api/admin/categories",
            json=test_data,
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        
        print(f"   状态码: {response.status_code}")
        print(f"   响应内容: {response.text}")
        
        if response.status_code in [200, 201]:
            print("   ✅ 分类创建成功")
            created_category = response.json()
            if created_category.get('success'):
                print(f"   创建的分类ID: {created_category['data'].get('id', 'N/A')}")
            else:
                print(f"   创建失败: {created_category.get('message')}")
                return False
        else:
            print("   ❌ 分类创建失败")
            return False
        
        # 4. 再次获取分类，验证是否增加
        print("\n4. 验证分类是否增加...")
        response = requests.get(f"{base_url}/api/categories", timeout=5)
        if response.status_code == 200:
            categories_after = response.json()
            print(f"   创建后分类数量: {len(categories_after)}")
            
            if len(categories_after) > len(categories_before):
                print("   ✅ 分类数量增加，MySQL API创建成功！")
                
                # 查找新创建的分类
                new_categories = [c for c in categories_after if c.get('title') == 'MySQL测试分类']
                if new_categories:
                    print(f"   新分类详情: {new_categories[0]}")
                
                return True
            else:
                print("   ❌ 分类数量未增加，MySQL保存可能有问题")
                return False
        else:
            print(f"   获取分类失败: {response.status_code}")
            return False
        
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败：Mock Server未运行在8001端口")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("修复后的Mock Server测试")
    print("=" * 60)
    
    success = test_fixed_mock_server()
    
    print("\n" + "=" * 60)
    print("测试结果")
    print("=" * 60)
    
    if success:
        print("🎉 Mock Server修复成功！")
        print("\n✅ 修复成果：")
        print("- 🧹 清理了JSON文件，统一使用MySQL")
        print("- 🔧 修复了分类创建API，使用MySQL存储")
        print("- 📊 分类创建和获取使用相同的MySQL数据源")
        print("- 🔄 API路由冲突已解决")
        print("\n🎯 现在客户端应该能正常工作：")
        print("- 本地数据库模式创建的分类会保存到本地MySQL")
        print("- 服务端模式创建的分类会保存到服务端MySQL")
        print("- 数据源切换会显示正确的数据差异")
    else:
        print("❌ Mock Server修复失败")
        print("\n需要检查：")
        print("- Mock Server是否正常启动")
        print("- MySQL数据库连接是否正常")
        print("- API路由是否正确配置")

if __name__ == "__main__":
    main()
