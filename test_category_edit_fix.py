#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分类编辑问题修正验证测试
验证分类编辑功能是否能正确从内存获取分类详情
"""

import sys
import os

# 添加src目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from services.course_service import CourseService
from cache.global_data_manager import global_data_manager
from database.models import DatabaseManager

def test_category_detail_fix():
    """测试分类详情获取修正"""
    print("🧪 测试分类详情获取修正...")
    
    # 1. 首先加载数据到内存
    print("\n📦 步骤1: 加载数据到内存...")
    success = global_data_manager.load_all_data_once()
    if not success:
        print("❌ 数据加载失败，无法继续测试")
        return False
    
    # 2. 检查内存中的分类数据
    print("\n📊 步骤2: 检查内存中的分类数据...")
    data_summary = global_data_manager.get_data_summary()
    print(f"内存数据统计: {data_summary}")
    
    if data_summary['category_count'] == 0:
        print("⚠️ 内存中没有分类数据，无法测试")
        return False
    
    # 3. 获取第一个分类的ID进行测试
    print("\n🔍 步骤3: 获取分类列表...")
    category_list_result = global_data_manager.get_category_list(page=1, page_size=5)
    if not category_list_result['success'] or not category_list_result['data']:
        print("❌ 获取分类列表失败")
        return False
    
    first_category = category_list_result['data'][0]
    category_id = first_category['id']
    category_title = first_category['title']
    print(f"测试分类: ID={category_id}, 标题={category_title}")
    
    # 4. 测试直接从全局数据管理器获取分类详情
    print(f"\n📂 步骤4: 直接从全局数据管理器获取分类详情...")
    detail_result = global_data_manager.get_category_detail(str(category_id))
    print(f"全局数据管理器结果: success={detail_result['success']}")
    if detail_result['success']:
        category_data = detail_result['data']
        print(f"分类详情: 标题={category_data['title']}, 视频数量={len(category_data.get('videos', []))}")
    else:
        print(f"❌ 获取失败: {detail_result['message']}")
        return False
    
    # 5. 测试通过CourseService获取分类详情（这是UI实际调用的方法）
    print(f"\n🔧 步骤5: 通过CourseService获取分类详情...")
    db_manager = DatabaseManager("sqlite:///test.db")  # 使用测试数据库
    course_service = CourseService(db_manager)
    service_result = course_service.get_category_detail(category_id)
    print(f"CourseService结果: success={service_result['success']}")
    if service_result['success']:
        category_data = service_result['data']
        print(f"分类详情: 标题={category_data['title']}, 视频数量={len(category_data.get('videos', []))}")
        print("✅ 分类编辑功能应该可以正常工作了！")
        return True
    else:
        print(f"❌ 获取失败: {service_result['message']}")
        return False

def test_multiple_categories():
    """测试多个分类的详情获取"""
    print("\n🔄 测试多个分类的详情获取...")
    
    # 获取分类列表
    category_list_result = global_data_manager.get_category_list(page=1, page_size=10)
    if not category_list_result['success']:
        print("❌ 获取分类列表失败")
        return False
    
    categories = category_list_result['data']
    print(f"找到 {len(categories)} 个分类进行测试")
    
    db_manager = DatabaseManager("sqlite:///test.db")  # 使用测试数据库
    course_service = CourseService(db_manager)
    success_count = 0
    
    for category in categories[:5]:  # 测试前5个分类
        category_id = category['id']
        category_title = category['title']
        
        result = course_service.get_category_detail(category_id)
        if result['success']:
            print(f"✅ 分类 {category_id} ({category_title}) 详情获取成功")
            success_count += 1
        else:
            print(f"❌ 分类 {category_id} ({category_title}) 详情获取失败: {result['message']}")
    
    print(f"\n📊 测试结果: {success_count}/{len(categories[:5])} 个分类详情获取成功")
    return success_count == len(categories[:5])

if __name__ == "__main__":
    print("=" * 60)
    print("分类编辑问题修正验证测试")
    print("=" * 60)
    
    try:
        # 测试分类详情获取修正
        fix_success = test_category_detail_fix()
        
        if fix_success:
            # 测试多个分类
            multiple_success = test_multiple_categories()
            
            if multiple_success:
                print("\n" + "=" * 60)
                print("🎉 所有测试通过！分类编辑功能已修正。")
                print("现在点击分类编辑按钮应该不会再提示'分类不存在'了。")
                print("=" * 60)
            else:
                print("\n⚠️ 部分分类测试失败，可能存在数据问题")
        else:
            print("\n❌ 分类详情获取修正测试失败")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
