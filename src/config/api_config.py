"""
API配置
"""

import os
from typing import Dict, Any

class APIConfig:
    """API配置类"""
    
    def __init__(self):
        # 从环境变量或配置文件读取，默认启用API模式
        self.use_api = os.getenv('USE_API', 'true').lower() == 'true'
        self.api_base_url = os.getenv('API_BASE_URL', 'http://localhost:8000')
        self.api_timeout = int(os.getenv('API_TIMEOUT', '30'))
    
    def get_config(self) -> Dict[str, Any]:
        """获取API配置"""
        return {
            'use_api': self.use_api,
            'base_url': self.api_base_url,
            'timeout': self.api_timeout
        }
    
    def set_api_mode(self, enabled: bool):
        """设置API模式"""
        self.use_api = enabled
    
    def set_api_url(self, url: str):
        """设置API地址"""
        self.api_base_url = url

    def is_api_mode(self) -> bool:
        """是否为API模式"""
        return self.use_api

    def is_local_mode(self) -> bool:
        """是否为本地数据库模式"""
        return not self.use_api

    def get_mode_name(self) -> str:
        """获取当前模式名称"""
        return "服务端API" if self.use_api else "本地数据库"

# 全局配置实例
api_config = APIConfig()
