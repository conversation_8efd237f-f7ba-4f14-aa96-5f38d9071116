#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全局数据管理器
实现一次性加载所有数据到内存，后续操作完全使用内存数据
"""

import logging
from typing import Dict, List, Any, Optional
from api.client import APIClient, SeriesAPIClient, CategoryAPIClient, VideoAPIClient

logger = logging.getLogger(__name__)

class GlobalDataManager:
    """全局数据管理器 - 一次性加载，内存操作"""
    
    def __init__(self, api_base_url: str = "http://localhost:8000"):
        self.api_client = APIClient(api_base_url)
        self.series_client = SeriesAPIClient(self.api_client)
        self.category_client = CategoryAPIClient(self.api_client)
        self.video_client = VideoAPIClient(self.api_client)
        
        # 内存数据存储
        self._series_data: List[Dict[str, Any]] = []
        self._category_data: List[Dict[str, Any]] = []
        self._video_data: List[Dict[str, Any]] = []
        
        # 加载状态
        self._data_loaded = False
        
    def load_all_data_once(self) -> bool:
        """一次性加载所有数据到内存"""
        if self._data_loaded:
            print("📦 数据已在内存中，跳过重复加载")
            return True
            
        try:
            print("🚀 开始一次性加载所有数据...")
            
            # 1. 加载系列数据
            print("📚 加载系列数据...")
            series_result = self.series_client.get_series(page=1, page_size=1000)
            if series_result.get('success'):
                self._series_data = series_result.get('data', [])
                print(f"✅ 系列数据加载完成: {len(self._series_data)} 个")
            else:
                print(f"❌ 系列数据加载失败: {series_result.get('message')}")
                return False
            
            # 2. 加载分类数据（无分页，获取所有数据）
            print("📂 加载分类数据...")
            category_result = self.category_client.get_all_categories()
            if category_result.get('success'):
                self._category_data = category_result.get('data', [])
                print(f"✅ 分类数据加载完成: {len(self._category_data)} 个")
            else:
                print(f"❌ 分类数据加载失败: {category_result.get('message')}")
                return False

            # 3. 加载视频数据（无分页，获取所有数据）
            print("📹 加载视频数据...")
            video_result = self.video_client.get_all_videos()
            if video_result.get('success'):
                self._video_data = video_result.get('data', [])
                print(f"✅ 视频数据加载完成: {len(self._video_data)} 个")
            else:
                print(f"❌ 视频数据加载失败: {video_result.get('message')}")
                return False
            
            self._data_loaded = True
            print("🎉 所有数据一次性加载完成！")
            print(f"📊 数据统计: 系列={len(self._series_data)}, 分类={len(self._category_data)}, 视频={len(self._video_data)}")
            return True
            
        except Exception as e:
            print(f"❌ 一次性数据加载异常: {e}")
            return False
    
    def get_series_list(self, page: int = 1, page_size: int = 20, 
                       search: Optional[str] = None) -> Dict[str, Any]:
        """从内存获取系列列表"""
        if not self._data_loaded:
            print("⚠️ 数据未加载，请先调用 load_all_data_once()")
            return {'success': False, 'message': '数据未加载', 'data': []}
        
        print(f"📚 从内存获取系列列表: page={page}, page_size={page_size}, search={search}")
        
        # 应用搜索筛选
        filtered_data = self._series_data
        if search:
            filtered_data = [s for s in filtered_data if search.lower() in s.get('title', '').lower()]
        
        # 应用分页
        total = len(filtered_data)
        start = (page - 1) * page_size
        end = start + page_size
        paginated_data = filtered_data[start:end]
        
        print(f"📚 内存系列数据: 总数={total}, 返回={len(paginated_data)}")
        
        return {
            'success': True,
            'data': paginated_data,
            'pagination': {
                'current_page': page,
                'page_size': page_size,
                'total_records': total,
                'total_pages': (total + page_size - 1) // page_size,
                'has_next': end < total,
                'has_prev': page > 1
            }
        }
    
    def get_category_list(self, page: int = 1, page_size: int = 20,
                         search: Optional[str] = None, series_id: Optional[str] = None) -> Dict[str, Any]:
        """从内存获取分类列表"""
        if not self._data_loaded:
            print("⚠️ 数据未加载，请先调用 load_all_data_once()")
            return {'success': False, 'message': '数据未加载', 'data': []}
        
        print(f"📂 从内存获取分类列表: page={page}, page_size={page_size}, search={search}, series_id={series_id}")
        
        # 应用筛选
        filtered_data = self._category_data
        if series_id:
            filtered_data = [c for c in filtered_data if c.get('series_id') == series_id]
        if search:
            filtered_data = [c for c in filtered_data if search.lower() in c.get('title', '').lower()]
        
        # 应用分页
        total = len(filtered_data)
        start = (page - 1) * page_size
        end = start + page_size
        paginated_data = filtered_data[start:end]
        
        print(f"📂 内存分类数据: 总数={total}, 返回={len(paginated_data)}")
        
        return {
            'success': True,
            'data': paginated_data,
            'pagination': {
                'current_page': page,
                'page_size': page_size,
                'total_records': total,
                'total_pages': (total + page_size - 1) // page_size,
                'has_next': end < total,
                'has_prev': page > 1
            }
        }
    
    def get_video_list(self, page: int = 1, page_size: int = 20,
                      search: Optional[str] = None, series_id: Optional[str] = None,
                      category_id: Optional[str] = None) -> Dict[str, Any]:
        """从内存获取视频列表"""
        if not self._data_loaded:
            print("⚠️ 数据未加载，请先调用 load_all_data_once()")
            return {'success': False, 'message': '数据未加载', 'data': []}

        print(f"📹 从内存获取视频列表: page={page}, page_size={page_size}, search={search}, series_id={series_id}, category_id={category_id}")

        # 应用筛选
        filtered_data = self._video_data
        if series_id:
            filtered_data = [v for v in filtered_data if v.get('series_id') == series_id]
        if category_id:
            filtered_data = [v for v in filtered_data if v.get('category_id') == category_id]
        if search:
            filtered_data = [v for v in filtered_data if search.lower() in v.get('title', '').lower()]

        # 应用分页
        total = len(filtered_data)
        start = (page - 1) * page_size
        end = start + page_size
        paginated_data = filtered_data[start:end]

        print(f"📹 内存视频数据: 总数={total}, 返回={len(paginated_data)}")

        return {
            'success': True,
            'data': paginated_data,
            'pagination': {
                'current_page': page,
                'page_size': page_size,
                'total_records': total,
                'total_pages': (total + page_size - 1) // page_size,
                'has_next': end < total,
                'has_prev': page > 1
            }
        }

    def get_category_detail(self, category_id: str) -> Dict[str, Any]:
        """从内存获取分类详情"""
        if not self._data_loaded:
            print("⚠️ 数据未加载，请先调用 load_all_data_once()")
            return {'success': False, 'message': '数据未加载', 'data': None}

        print(f"📂 从内存获取分类详情: category_id={category_id}")

        # 查找分类
        category = None
        for cat in self._category_data:
            if str(cat.get('id')) == str(category_id):
                category = cat.copy()  # 复制一份避免修改原数据
                break

        if not category:
            print(f"❌ 内存中未找到分类 {category_id}")
            return {
                'success': False,
                'message': '分类不存在',
                'data': None
            }

        # 获取分类下的视频
        category_videos = [v for v in self._video_data if str(v.get('category_id')) == str(category_id)]
        category['videos'] = category_videos

        print(f"✅ 从内存获取分类详情成功: {category.get('title')}, 视频数量: {len(category_videos)}")

        return {
            'success': True,
            'data': category,
            'message': '获取分类详情成功'
        }
    
    def is_data_loaded(self) -> bool:
        """检查数据是否已加载"""
        return self._data_loaded
    
    def get_data_summary(self) -> Dict[str, int]:
        """获取数据统计"""
        return {
            'series_count': len(self._series_data),
            'category_count': len(self._category_data),
            'video_count': len(self._video_data),
            'loaded': self._data_loaded
        }

    def force_reload_data(self) -> bool:
        """强制重新加载所有数据（忽略缓存状态）"""
        print("🔄 强制重新加载所有数据...")

        # 重置加载状态
        self._data_loaded = False

        # 清空内存数据
        self._series_data.clear()
        self._category_data.clear()
        self._video_data.clear()

        # 重新加载数据
        return self.load_all_data_once()

    def clear_cache(self):
        """清除内存缓存"""
        print("🗑️ 清除内存缓存...")
        self._data_loaded = False
        self._series_data.clear()
        self._category_data.clear()
        self._video_data.clear()
        print("✅ 内存缓存已清除")

    def add_series_to_memory(self, series_data: dict):
        """添加新系列到内存缓存"""
        try:
            self._series_data.append(series_data)
            print(f"💾 新系列已添加到全局内存缓存: {series_data['title']}")
        except Exception as e:
            print(f"❌ 添加系列到全局内存缓存失败: {e}")

    def remove_series_from_memory(self, series_id: str):
        """从内存缓存中移除系列"""
        try:
            self._series_data = [s for s in self._series_data if s['id'] != series_id]
            print(f"🗑️ 系列已从全局内存缓存中移除: {series_id}")
        except Exception as e:
            print(f"❌ 从全局内存缓存移除系列失败: {e}")

    def add_category_to_memory(self, category_data: dict):
        """添加新分类到内存缓存"""
        try:
            self._category_data.append(category_data)
            print(f"💾 新分类已添加到全局内存缓存: {category_data['title']}")
        except Exception as e:
            print(f"❌ 添加分类到全局内存缓存失败: {e}")

    def remove_category_from_memory(self, category_id: str):
        """从内存缓存中移除分类"""
        try:
            self._category_data = [c for c in self._category_data if c['id'] != category_id]
            print(f"🗑️ 分类已从全局内存缓存中移除: {category_id}")
        except Exception as e:
            print(f"❌ 从全局内存缓存移除分类失败: {e}")

    def update_series_in_memory(self, series_id: str, series_data: Dict[str, Any]) -> bool:
        """更新内存中的系列数据"""
        try:
            for i, series in enumerate(self._series_data):
                if str(series.get('id')) == str(series_id):
                    self._series_data[i].update(series_data)
                    print(f"✅ 内存中系列 {series_id} 数据已更新")
                    return True
            print(f"⚠️ 内存中未找到系列 {series_id}")
            return False
        except Exception as e:
            print(f"❌ 更新内存系列数据失败: {e}")
            return False

    def update_category_in_memory(self, category_id: str, category_data: Dict[str, Any]) -> bool:
        """更新内存中的分类数据"""
        try:
            for i, category in enumerate(self._category_data):
                if str(category.get('id')) == str(category_id):
                    self._category_data[i].update(category_data)
                    print(f"✅ 内存中分类 {category_id} 数据已更新")
                    return True
            print(f"⚠️ 内存中未找到分类 {category_id}")
            return False
        except Exception as e:
            print(f"❌ 更新内存分类数据失败: {e}")
            return False

    def update_video_in_memory(self, video_id: str, video_data: Dict[str, Any]) -> bool:
        """更新内存中的视频数据"""
        try:
            for i, video in enumerate(self._video_data):
                if str(video.get('id')) == str(video_id):
                    self._video_data[i].update(video_data)
                    print(f"✅ 内存中视频 {video_id} 数据已更新")
                    return True
            print(f"⚠️ 内存中未找到视频 {video_id}")
            return False
        except Exception as e:
            print(f"❌ 更新内存视频数据失败: {e}")
            return False

    def add_series_to_memory(self, series_data: Dict[str, Any]) -> bool:
        """添加系列到内存"""
        try:
            self._series_data.append(series_data)
            print(f"✅ 系列 {series_data.get('id')} 已添加到内存")
            return True
        except Exception as e:
            print(f"❌ 添加系列到内存失败: {e}")
            return False

    def add_category_to_memory(self, category_data: Dict[str, Any]) -> bool:
        """添加分类到内存"""
        try:
            self._category_data.append(category_data)
            print(f"✅ 分类 {category_data.get('id')} 已添加到内存")
            return True
        except Exception as e:
            print(f"❌ 添加分类到内存失败: {e}")
            return False

    def add_video_to_memory(self, video_data: Dict[str, Any]) -> bool:
        """添加视频到内存"""
        try:
            self._video_data.append(video_data)
            print(f"✅ 视频 {video_data.get('id')} 已添加到内存")
            return True
        except Exception as e:
            print(f"❌ 添加视频到内存失败: {e}")
            return False

    def remove_series_from_memory(self, series_id: str) -> bool:
        """从内存中删除系列"""
        try:
            for i, series in enumerate(self._series_data):
                if str(series.get('id')) == str(series_id):
                    del self._series_data[i]
                    print(f"✅ 系列 {series_id} 已从内存中删除")
                    return True
            print(f"⚠️ 内存中未找到系列 {series_id}")
            return False
        except Exception as e:
            print(f"❌ 从内存删除系列失败: {e}")
            return False

    def remove_category_from_memory(self, category_id: str) -> bool:
        """从内存中删除分类"""
        try:
            for i, category in enumerate(self._category_data):
                if str(category.get('id')) == str(category_id):
                    del self._category_data[i]
                    print(f"✅ 分类 {category_id} 已从内存中删除")
                    return True
            print(f"⚠️ 内存中未找到分类 {category_id}")
            return False
        except Exception as e:
            print(f"❌ 从内存删除分类失败: {e}")
            return False

    def remove_video_from_memory(self, video_id: str) -> bool:
        """从内存中删除视频"""
        try:
            for i, video in enumerate(self._video_data):
                if str(video.get('id')) == str(video_id):
                    del self._video_data[i]
                    print(f"✅ 视频 {video_id} 已从内存中删除")
                    return True
            print(f"⚠️ 内存中未找到视频 {video_id}")
            return False
        except Exception as e:
            print(f"❌ 从内存删除视频失败: {e}")
            return False

# 全局单例实例
global_data_manager = GlobalDataManager()
