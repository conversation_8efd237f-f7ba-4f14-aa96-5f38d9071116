#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
同步管理器
实现异步三重同步：本地数据库 + 内存缓存 + 服务器同步
"""

import threading
import time
import logging
from typing import Dict, Any, List, Callable
from queue import Queue
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class SyncStatus(Enum):
    """同步状态枚举"""
    PENDING = "pending"      # 待同步
    SYNCING = "syncing"      # 同步中
    SYNCED = "synced"        # 已同步
    FAILED = "failed"        # 同步失败

@dataclass
class SyncTask:
    """同步任务"""
    entity_type: str  # 'series', 'category', 'video'
    entity_id: str
    data: Dict[str, Any]
    attempt_count: int = 0
    max_retries: int = 3

class SyncManager:
    """异步同步管理器"""
    
    def __init__(self):
        self.sync_status: Dict[str, SyncStatus] = {}
        self.retry_queue: Queue = Queue()
        self.manual_sync_queue: List[SyncTask] = []
        self.api_clients = {}
        self.status_callbacks: List[Callable] = []

        # 初始化新的数据同步管理器
        try:
            from sync.data_sync_manager import DataSyncManager
            self.data_sync_manager = DataSyncManager(
                local_db_config={
                    'host': 'localhost',
                    'user': 'mike',
                    'password': 'dyj217',
                    'database': 'shuimu_course',
                    'charset': 'utf8mb4'
                },
                api_base_url='http://localhost:8000'
            )
            print("✅ 新的数据同步管理器初始化成功")
        except Exception as e:
            print(f"⚠️ 新的数据同步管理器初始化失败: {e}")
            self.data_sync_manager = None

        # 启动后台同步线程
        self._start_background_worker()
    
    def set_api_clients(self, series_api, category_api, video_api):
        """设置API客户端"""
        self.api_clients = {
            'series': series_api,
            'category': category_api,
            'video': video_api
        }
    
    def add_status_callback(self, callback: Callable):
        """添加状态变化回调"""
        self.status_callbacks.append(callback)
    
    def _notify_status_change(self, entity_type: str, entity_id: str, status: SyncStatus):
        """通知状态变化"""
        key = f"{entity_type}_{entity_id}"
        self.sync_status[key] = status
        
        # 调用所有回调函数
        for callback in self.status_callbacks:
            try:
                callback(entity_type, entity_id, status)
            except Exception as e:
                logger.error(f"状态回调异常: {e}")
    
    def async_sync_to_server(self, entity_type: str, entity_id: str, data: Dict[str, Any]):
        """异步同步到服务器"""
        task = SyncTask(entity_type, entity_id, data)
        
        # 标记为同步中
        self._notify_status_change(entity_type, entity_id, SyncStatus.SYNCING)
        
        # 添加到重试队列
        self.retry_queue.put(task)
        
        print(f"📤 {entity_type} {entity_id} 已加入异步同步队列")
    
    def _start_background_worker(self):
        """启动后台同步工作线程"""
        def worker():
            while True:
                try:
                    # 从队列获取任务（阻塞等待）
                    task = self.retry_queue.get(timeout=1)
                    self._process_sync_task(task)
                    self.retry_queue.task_done()
                except:
                    # 队列为空或超时，继续循环
                    continue
        
        # 启动守护线程
        worker_thread = threading.Thread(target=worker, daemon=True)
        worker_thread.start()
        print("🔄 异步同步后台线程已启动")
    
    def _process_sync_task(self, task: SyncTask):
        """处理同步任务"""
        retry_delays = [1, 3, 5]  # 重试间隔（秒）
        
        while task.attempt_count < task.max_retries:
            task.attempt_count += 1
            
            try:
                print(f"🔄 {task.entity_type} {task.entity_id} 开始同步到服务器（第{task.attempt_count}次尝试）")

                # 优先使用新的数据同步管理器
                if self.data_sync_manager:
                    sync_result = self.data_sync_manager.sync_to_server(task.entity_type, task.entity_id, task.data)

                    if sync_result.get('success'):
                        print(f"✅ {task.entity_type} {task.entity_id} 服务器同步成功（第{task.attempt_count}次尝试）")
                        self._notify_status_change(task.entity_type, task.entity_id, SyncStatus.SYNCED)
                        return  # 成功，退出重试循环
                    else:
                        error_msg = sync_result.get('message', '未知错误')
                        print(f"⚠️ {task.entity_type} {task.entity_id} 服务器同步失败（第{task.attempt_count}次尝试）: {error_msg}")

                else:
                    # 回退到旧的API客户端
                    api_client = self.api_clients.get(task.entity_type)
                    if not api_client:
                        raise Exception(f"未找到 {task.entity_type} 的API客户端")

                    # 执行API调用
                    if task.entity_type == 'series':
                        api_result = api_client.update_series(task.entity_id, task.data)
                    elif task.entity_type == 'category':
                        api_result = api_client.update_category(task.entity_id, task.data)
                    elif task.entity_type == 'video':
                        api_result = api_client.update_video(task.entity_id, task.data)
                    else:
                        raise Exception(f"不支持的实体类型: {task.entity_type}")

                    # 检查API调用结果
                    if api_result.get('success'):
                        print(f"✅ {task.entity_type} {task.entity_id} 服务器同步成功（第{task.attempt_count}次尝试）")
                        self._notify_status_change(task.entity_type, task.entity_id, SyncStatus.SYNCED)
                        return  # 成功，退出重试循环
                    else:
                        error_msg = api_result.get('message', '未知错误')
                        print(f"⚠️ {task.entity_type} {task.entity_id} 服务器同步失败（第{task.attempt_count}次尝试）: {error_msg}")
                    
            except Exception as e:
                print(f"❌ {task.entity_type} {task.entity_id} 服务器同步异常（第{task.attempt_count}次尝试）: {e}")
            
            # 如果不是最后一次尝试，等待后重试
            if task.attempt_count < task.max_retries:
                delay = retry_delays[min(task.attempt_count - 1, len(retry_delays) - 1)]
                print(f"⏳ {task.entity_type} {task.entity_id} 等待 {delay} 秒后重试...")
                time.sleep(delay)
        
        # 所有重试都失败
        print(f"💀 {task.entity_type} {task.entity_id} 服务器同步最终失败，标记为待手动同步")
        self._notify_status_change(task.entity_type, task.entity_id, SyncStatus.FAILED)
        self.manual_sync_queue.append(task)
    
    def get_sync_status(self, entity_type: str, entity_id: str) -> SyncStatus:
        """获取同步状态"""
        key = f"{entity_type}_{entity_id}"
        return self.sync_status.get(key, SyncStatus.SYNCED)  # 默认为已同步
    
    def get_failed_sync_tasks(self) -> List[SyncTask]:
        """获取失败的同步任务"""
        return self.manual_sync_queue.copy()
    
    def retry_failed_task(self, task: SyncTask):
        """重试失败的任务"""
        task.attempt_count = 0  # 重置尝试次数
        self.manual_sync_queue.remove(task)
        self.async_sync_to_server(task.entity_type, task.entity_id, task.data)
        print(f"🔄 {task.entity_type} {task.entity_id} 已重新加入同步队列")
    
    def clear_failed_tasks(self):
        """清除失败的任务"""
        count = len(self.manual_sync_queue)
        self.manual_sync_queue.clear()
        print(f"🗑️ 已清除 {count} 个失败的同步任务")
    
    def get_sync_statistics(self) -> Dict[str, int]:
        """获取同步统计信息"""
        stats = {
            'pending': 0,
            'syncing': 0,
            'synced': 0,
            'failed': 0
        }
        
        for status in self.sync_status.values():
            stats[status.value] += 1
        
        stats['failed'] = len(self.manual_sync_queue)
        stats['queue_size'] = self.retry_queue.qsize()
        
        return stats

# 全局同步管理器实例
sync_manager = SyncManager()
