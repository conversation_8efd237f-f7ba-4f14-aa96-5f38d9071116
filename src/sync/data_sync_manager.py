#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据同步管理器
实现管理端与服务端的数据同步
"""

import requests
import mysql.connector
from typing import Dict, Any, Optional
import json

class DataSyncManager:
    def __init__(self, local_db_config: Dict, api_base_url: str):
        self.local_db_config = local_db_config
        self.api_base_url = api_base_url.rstrip('/')
        self.timeout = 10
    
    def sync_to_server(self, table_name: str, record_id: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """同步单条记录到服务端"""
        try:
            endpoint_map = {
                'series': f"/api/series/{record_id}",
                'categories': f"/api/categories/{record_id}",
                'videos': f"/api/videos/{record_id}",
                'users': f"/api/users/{record_id}"
            }
            
            if table_name not in endpoint_map:
                return {
                    'success': False,
                    'message': f'不支持的表名: {table_name}'
                }
            
            url = f"{self.api_base_url}{endpoint_map[table_name]}"
            response = requests.put(url, json=data, timeout=self.timeout)
            
            if response.status_code == 200:
                return {
                    'success': True,
                    'message': f'{table_name} {record_id} 同步成功',
                    'data': response.json()
                }
            else:
                return {
                    'success': False,
                    'message': f'同步失败: HTTP {response.status_code}',
                    'details': response.text
                }
                
        except requests.exceptions.Timeout:
            return {
                'success': False,
                'message': '同步超时，请检查网络连接'
            }
        except requests.exceptions.ConnectionError:
            return {
                'success': False,
                'message': '无法连接到服务端，请检查服务端是否运行'
            }
        except Exception as e:
            return {
                'success': False,
                'message': f'同步异常: {str(e)}'
            }
    
    def sync_from_server(self) -> Dict[str, Any]:
        """从服务端同步所有数据到本地"""
        try:
            with mysql.connector.connect(**self.local_db_config) as conn:
                cursor = conn.cursor()
                
                sync_results = {
                    'series': 0,
                    'categories': 0,
                    'videos': 0,
                    'errors': []
                }
                
                # 同步系列数据
                try:
                    series_response = requests.get(f"{self.api_base_url}/api/series", timeout=self.timeout)
                    if series_response.status_code == 200:
                        series_list = series_response.json()
                        
                        for series in series_list:
                            cursor.execute("""
                                INSERT INTO series (id, title, description, price, is_published, updated_at)
                                VALUES (%s, %s, %s, %s, %s, NOW())
                                ON DUPLICATE KEY UPDATE
                                title=VALUES(title), description=VALUES(description), 
                                price=VALUES(price), is_published=VALUES(is_published), updated_at=NOW()
                            """, (
                                series['id'],
                                series['title'],
                                series.get('description', ''),
                                series.get('price', 0),
                                series.get('is_published', 1)
                            ))
                        
                        sync_results['series'] = len(series_list)
                except Exception as e:
                    sync_results['errors'].append(f"同步系列失败: {str(e)}")
                
                # 同步分类数据
                try:
                    categories_response = requests.get(f"{self.api_base_url}/api/categories", timeout=self.timeout)
                    if categories_response.status_code == 200:
                        categories_list = categories_response.json()
                        
                        for category in categories_list:
                            cursor.execute("""
                                INSERT INTO categories (id, series_id, title, description, price, order_index, updated_at)
                                VALUES (%s, %s, %s, %s, %s, %s, NOW())
                                ON DUPLICATE KEY UPDATE
                                title=VALUES(title), description=VALUES(description), 
                                price=VALUES(price), order_index=VALUES(order_index), updated_at=NOW()
                            """, (
                                category['id'],
                                category.get('seriesId', category.get('series_id')),
                                category['title'],
                                category.get('description', ''),
                                category.get('price', 0),
                                category.get('order_index', 0)
                            ))
                        
                        sync_results['categories'] = len(categories_list)
                except Exception as e:
                    sync_results['errors'].append(f"同步分类失败: {str(e)}")
                
                # 同步视频数据
                try:
                    videos_response = requests.get(f"{self.api_base_url}/api/videos", timeout=self.timeout)
                    if videos_response.status_code == 200:
                        videos_list = videos_response.json()
                        
                        for video in videos_list:
                            cursor.execute("""
                                INSERT INTO videos (id, category_id, title, description, video_url, duration, order_index, updated_at)
                                VALUES (%s, %s, %s, %s, %s, %s, %s, NOW())
                                ON DUPLICATE KEY UPDATE
                                title=VALUES(title), description=VALUES(description), 
                                video_url=VALUES(video_url), duration=VALUES(duration), 
                                order_index=VALUES(order_index), updated_at=NOW()
                            """, (
                                video['id'],
                                video.get('categoryId', video.get('category_id')),
                                video['title'],
                                video.get('description', ''),
                                video.get('cloudUrl', video.get('video_url', '')),
                                video.get('duration', 0),
                                video.get('order_index', 0)
                            ))
                        
                        sync_results['videos'] = len(videos_list)
                except Exception as e:
                    sync_results['errors'].append(f"同步视频失败: {str(e)}")
                
                conn.commit()
                
                if sync_results['errors']:
                    return {
                        'success': False,
                        'message': '部分数据同步失败',
                        'data': sync_results
                    }
                else:
                    return {
                        'success': True,
                        'message': f"同步成功: 系列{sync_results['series']}个, 分类{sync_results['categories']}个, 视频{sync_results['videos']}个",
                        'data': sync_results
                    }
                    
        except requests.exceptions.ConnectionError:
            return {
                'success': False,
                'message': '无法连接到服务端，请检查服务端是否运行'
            }
        except Exception as e:
            return {
                'success': False,
                'message': f'从服务端同步失败: {str(e)}'
            }
    
    def test_connection(self) -> Dict[str, Any]:
        """测试与服务端的连接"""
        try:
            response = requests.get(f"{self.api_base_url}/", timeout=5)
            if response.status_code == 200:
                return {
                    'success': True,
                    'message': '服务端连接正常'
                }
            else:
                return {
                    'success': False,
                    'message': f'服务端响应异常: HTTP {response.status_code}'
                }
        except requests.exceptions.ConnectionError:
            return {
                'success': False,
                'message': '无法连接到服务端'
            }
        except Exception as e:
            return {
                'success': False,
                'message': f'连接测试失败: {str(e)}'
            }
