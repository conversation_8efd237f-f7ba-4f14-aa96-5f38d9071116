#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主窗口模块
"""

import sys
from PyQt6.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                            QLabel, QPushButton, QGroupBox, QTableWidget, 
                            QTableWidgetItem, QHeaderView, QMessageBox,
                            QStatusBar, QMenuBar, QToolBar)
from PyQt6.QtCore import Qt, QTimer, QThread, pyqtSignal
from PyQt6.QtGui import QAction, QIcon, QFont

from database.connection import DatabaseConnection
from database.models import DatabaseManager
from ui.user_window import UserManagementWindow
from utils.config import Config
from sync.data_sync_manager import DataSyncManager

class DataLoader(QThread):
    """数据加载线程"""
    data_loaded = pyqtSignal(dict)
    error_occurred = pyqtSignal(str)
    
    def __init__(self, db_connection):
        super().__init__()
        self.db_connection = db_connection
    
    def run(self):
        """加载数据"""
        try:
            data = {}
            
            # 获取用户数量
            result = self.db_connection.execute_query("SELECT COUNT(*) FROM users")
            data['user_count'] = result[0][0] if result else 0
            
            # 获取系列数量
            result = self.db_connection.execute_query("SELECT COUNT(*) FROM series")
            data['series_count'] = result[0][0] if result else 0
            
            # 获取视频数量
            result = self.db_connection.execute_query("SELECT COUNT(*) FROM videos")
            data['video_count'] = result[0][0] if result else 0
            
            # 获取订单数量
            result = self.db_connection.execute_query("SELECT COUNT(*) FROM orders")
            data['order_count'] = result[0][0] if result else 0
            
            self.data_loaded.emit(data)
            
        except Exception as e:
            self.error_occurred.emit(str(e))

class MainWindow(QMainWindow):
    """主窗口类"""
    
    def __init__(self, config: Config):
        super().__init__()
        self.config = config
        self.db_connection = None
        self.db_manager = None
        self.data_loader = None

        # 初始化同步管理器
        self.sync_manager = DataSyncManager(
            local_db_config={
                'host': 'localhost',
                'user': 'mike',
                'password': 'dyj217',
                'database': 'shuimu_course',
                'charset': 'utf8mb4'
            },
            api_base_url='http://localhost:8000'
        )

        self.init_ui()
        self.init_database()
        self.setup_timer()
    
    def init_ui(self):
        """初始化用户界面"""
        app_config = self.config.get_app_config()

        # 设置窗口属性
        self.setWindowTitle(app_config['title'])
        self.setGeometry(100, 100, app_config['window_width'], app_config['window_height'])

        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 创建主布局（水平布局）
        main_layout = QHBoxLayout()
        central_widget.setLayout(main_layout)

        # 创建左侧菜单区域
        self.create_left_menu(main_layout)

        # 创建右侧内容区域
        self.create_right_content(main_layout)

        # 创建状态栏
        self.create_status_bar()

        # 创建菜单栏
        self.create_menu_bar()

        # 创建数据源切换栏
        self.create_data_source_toolbar()

        # 默认显示概览页面
        self.show_overview_page()
    
    def create_left_menu(self, parent_layout):
        """创建左侧菜单区域"""
        # 左侧菜单容器
        menu_widget = QWidget()
        menu_widget.setFixedWidth(200)
        menu_widget.setStyleSheet("""
            QWidget {
                background-color: #f5f5f5;
                border-right: 1px solid #ddd;
            }
        """)

        menu_layout = QVBoxLayout()
        menu_widget.setLayout(menu_layout)

        # 菜单标题
        menu_title = QLabel("功能菜单")
        menu_title.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                padding: 15px;
                background-color: #e9e9e9;
                border-bottom: 1px solid #ddd;
            }
        """)
        menu_layout.addWidget(menu_title)

        # 菜单按钮
        self.overview_btn = self.create_menu_button("📊 数据概览", True)
        self.user_btn = self.create_menu_button("👥 用户管理")
        self.course_btn = self.create_menu_button("📚 课程管理")
        self.order_btn = self.create_menu_button("💰 订单管理")
        self.settings_btn = self.create_menu_button("⚙️ 系统设置")

        # 连接信号
        self.overview_btn.clicked.connect(self.show_overview_page)
        self.user_btn.clicked.connect(self.show_user_page)
        self.course_btn.clicked.connect(self.show_course_page)
        self.order_btn.clicked.connect(self.show_order_page)
        self.settings_btn.clicked.connect(self.show_settings_page)

        menu_layout.addWidget(self.overview_btn)
        menu_layout.addWidget(self.user_btn)
        menu_layout.addWidget(self.course_btn)
        menu_layout.addWidget(self.order_btn)
        menu_layout.addWidget(self.settings_btn)
        menu_layout.addStretch()

        parent_layout.addWidget(menu_widget)

    def create_right_content(self, parent_layout):
        """创建右侧内容区域"""
        # 右侧内容容器
        self.content_widget = QWidget()
        self.content_layout = QVBoxLayout()
        self.content_widget.setLayout(self.content_layout)

        parent_layout.addWidget(self.content_widget)
    
    def create_menu_button(self, text: str, active: bool = False) -> QPushButton:
        """创建菜单按钮"""
        btn = QPushButton(text)
        btn.setFixedHeight(45)

        if active:
            btn.setStyleSheet("""
                QPushButton {
                    text-align: left;
                    padding: 10px 15px;
                    font-size: 14px;
                    border: none;
                    background-color: #007acc;
                    color: white;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #005a9e;
                }
            """)
        else:
            btn.setStyleSheet("""
                QPushButton {
                    text-align: left;
                    padding: 10px 15px;
                    font-size: 14px;
                    border: none;
                    background-color: transparent;
                    color: #333;
                }
                QPushButton:hover {
                    background-color: #e0e0e0;
                }
                QPushButton:pressed {
                    background-color: #d0d0d0;
                }
            """)

        return btn

    def create_stat_card(self, icon: str, title: str, value: str) -> QLabel:
        """创建统计卡片"""
        card = QLabel()
        card.setText(f"{icon} {title}: {value}")
        card.setAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter)
        card.setStyleSheet("""
            QLabel {
                border: 1px solid #e0e0e0;
                border-radius: 6px;
                padding: 8px 12px;
                background-color: #f9f9f9;
                font-size: 14px;
                font-weight: bold;
                min-height: 25px;
                max-height: 25px;
            }
        """)
        return card
    
    def show_overview_page(self):
        """显示概览页面"""
        self.clear_content()
        self.set_active_menu(self.overview_btn)

        # 创建概览页面布局
        overview_layout = QVBoxLayout()

        # 页面标题
        title = QLabel("📊 数据概览")
        title.setStyleSheet("""
            QLabel {
                font-size: 20px;
                font-weight: bold;
                padding: 15px 0;
                color: #333;
            }
        """)
        overview_layout.addWidget(title)

        # 数据概览卡片
        cards_layout = QHBoxLayout()

        # 创建统计卡片
        self.user_count_label = self.create_stat_card("👥", "用户数", "加载中...")
        self.series_count_label = self.create_stat_card("📚", "系列数", "加载中...")
        self.video_count_label = self.create_stat_card("🎬", "视频数", "加载中...")
        self.order_count_label = self.create_stat_card("💰", "订单数", "加载中...")

        cards_layout.addWidget(self.user_count_label)
        cards_layout.addWidget(self.series_count_label)
        cards_layout.addWidget(self.video_count_label)
        cards_layout.addWidget(self.order_count_label)
        cards_layout.addStretch()

        overview_layout.addLayout(cards_layout)

        # 数据表格区域
        self.create_overview_table(overview_layout)

        # 添加到内容区域
        overview_widget = QWidget()
        overview_widget.setLayout(overview_layout)
        self.content_layout.addWidget(overview_widget)

        # 加载数据
        self.load_data()

    def show_user_page(self):
        """显示用户管理页面"""
        self.clear_content()
        self.set_active_menu(self.user_btn)

        try:
            if hasattr(self, 'db_manager') and self.db_manager:
                # 创建用户管理页面
                from ui.user_window import UserManagementWindow
                user_widget = UserManagementWindow(self.db_manager)

                # 移除窗口装饰，作为widget嵌入
                user_widget.setWindowFlags(Qt.WindowType.Widget)

                self.content_layout.addWidget(user_widget)
            else:
                self.show_error_page("数据库连接未初始化，请检查数据库配置")
        except Exception as e:
            self.show_error_page(f"加载用户管理失败: {str(e)}")
            print(f"用户管理加载错误: {e}")

    def show_course_page(self):
        """显示课程管理页面 - 单例模式"""
        self.clear_content()
        self.set_active_menu(self.course_btn)

        try:
            if hasattr(self, 'db_manager') and self.db_manager:
                # 使用单例模式的课程管理页面
                if not hasattr(self, '_course_widget') or self._course_widget is None:
                    print("📚 首次创建课程管理页面...")
                    from ui.course_window import CourseManagementWindow
                    self._course_widget = CourseManagementWindow(self.db_manager)
                    # 移除窗口装饰，作为widget嵌入
                    self._course_widget.setWindowFlags(Qt.WindowType.Widget)
                else:
                    print("📚 显示已缓存的课程管理页面...")

                # 总是重新添加到布局中（因为clear_content已经移除了）
                self.content_layout.addWidget(self._course_widget)
                self._course_widget.show()
            else:
                self.show_error_page("数据库连接未初始化，请检查数据库配置")
        except Exception as e:
            self.show_error_page(f"加载课程管理失败: {str(e)}")
            print(f"课程管理加载错误: {e}")

    def show_order_page(self):
        """显示订单管理页面"""
        self.clear_content()
        self.set_active_menu(self.order_btn)

        try:
            if hasattr(self, 'db_manager') and self.db_manager:
                # 创建订单管理页面
                from ui.order_window import OrderManagementWindow
                order_widget = OrderManagementWindow(self.db_manager)

                # 移除窗口装饰，作为widget嵌入
                order_widget.setWindowFlags(Qt.WindowType.Widget)

                self.content_layout.addWidget(order_widget)
            else:
                self.show_error_page("数据库连接未初始化，请检查数据库配置")
        except Exception as e:
            self.show_error_page(f"加载订单管理失败: {str(e)}")
            print(f"订单管理加载错误: {e}")

    def show_settings_page(self):
        """显示系统设置页面"""
        self.clear_content()
        self.set_active_menu(self.settings_btn)
        self.show_placeholder_page("⚙️ 系统设置", "系统设置功能正在开发中...")
    
    def clear_content(self):
        """清空内容区域 - 正确的缓存版本"""
        # 从布局中移除所有widget，但保持widget对象的引用
        while self.content_layout.count():
            child = self.content_layout.takeAt(0)
            if child.widget():
                widget = child.widget()
                widget.setParent(None)  # 从布局中移除，但不删除widget对象

    def set_active_menu(self, active_btn):
        """设置活动菜单按钮"""
        # 重置所有按钮样式
        for btn in [self.overview_btn, self.user_btn, self.course_btn, self.order_btn, self.settings_btn]:
            btn.setStyleSheet("""
                QPushButton {
                    text-align: left;
                    padding: 10px 15px;
                    font-size: 14px;
                    border: none;
                    background-color: transparent;
                    color: #333;
                }
                QPushButton:hover {
                    background-color: #e0e0e0;
                }
                QPushButton:pressed {
                    background-color: #d0d0d0;
                }
            """)

        # 设置活动按钮样式
        active_btn.setStyleSheet("""
            QPushButton {
                text-align: left;
                padding: 10px 15px;
                font-size: 14px;
                border: none;
                background-color: #007acc;
                color: white;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #005a9e;
            }
        """)

    def show_placeholder_page(self, title: str, message: str):
        """显示占位页面"""
        placeholder_layout = QVBoxLayout()

        # 标题
        title_label = QLabel(title)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 20px;
                font-weight: bold;
                padding: 15px 0;
                color: #333;
            }
        """)
        placeholder_layout.addWidget(title_label)

        # 消息
        message_label = QLabel(message)
        message_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                color: #666;
                padding: 50px;
                text-align: center;
            }
        """)
        message_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        placeholder_layout.addWidget(message_label)

        placeholder_layout.addStretch()

        placeholder_widget = QWidget()
        placeholder_widget.setLayout(placeholder_layout)
        self.content_layout.addWidget(placeholder_widget)

    def show_error_page(self, error_msg: str):
        """显示错误页面"""
        self.show_placeholder_page("❌ 错误", error_msg)

    def create_overview_table(self, parent_layout):
        """创建概览表格区域"""
        table_group = QGroupBox("📋 最近活动")
        table_layout = QVBoxLayout()

        # 创建表格
        self.table = QTableWidget()
        self.table.setAlternatingRowColors(True)
        self.table.horizontalHeader().setStretchLastSection(True)

        # 设置表格数据（示例）
        self.table.setColumnCount(4)
        self.table.setHorizontalHeaderLabels(['时间', '类型', '内容', '状态'])
        self.table.setRowCount(3)

        # 示例数据
        sample_data = [
            ['2025-06-28 10:30', '用户注册', 'test_user 注册了账户', '成功'],
            ['2025-06-28 10:25', '订单创建', '用户购买了Java基础教程', '已完成'],
            ['2025-06-28 10:20', '系列发布', 'Python数据分析 系列已发布', '成功']
        ]

        for row, data in enumerate(sample_data):
            for col, value in enumerate(data):
                self.table.setItem(row, col, QTableWidgetItem(value))

        table_layout.addWidget(self.table)
        table_group.setLayout(table_layout)
        parent_layout.addWidget(table_group)
    
    def create_status_bar(self):
        """创建状态栏"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("准备就绪")
    
    def create_menu_bar(self):
        """创建菜单栏"""
        menubar = self.menuBar()
        
        # 文件菜单
        file_menu = menubar.addMenu('文件')
        
        # 数据库连接
        connect_action = QAction('连接数据库', self)
        connect_action.triggered.connect(self.connect_database)
        file_menu.addAction(connect_action)
        
        # 退出
        exit_action = QAction('退出', self)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # 帮助菜单
        help_menu = menubar.addMenu('帮助')
        about_action = QAction('关于', self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def init_database(self):
        """初始化数据库连接"""
        try:
            db_config = self.config.get_database_config()
            self.db_connection = DatabaseConnection(db_config)

            # 创建数据库管理器
            connection_string = f"mysql+pymysql://{db_config['user']}:{db_config['password']}@{db_config['host']}:{db_config['port']}/{db_config['database']}?charset={db_config['charset']}"
            self.db_manager = DatabaseManager(connection_string)

            # 创建表
            self.db_manager.create_tables()

            # 执行数据库迁移
            self._run_migration(db_config)

            self.connect_database()
        except Exception as e:
            print(f"数据库初始化失败: {e}")
            self.status_bar.showMessage(f"❌ 数据库初始化失败: {str(e)}")
            # 继续运行程序，但数据库功能不可用

    def _run_migration(self, db_config):
        """执行数据库迁移"""
        try:
            import pymysql
            connection = pymysql.connect(
                host=db_config['host'],
                port=db_config['port'],
                user=db_config['user'],
                password=db_config['password'],
                database=db_config['database'],
                charset=db_config['charset']
            )
            cursor = connection.cursor()

            # 检查是否需要迁移
            cursor.execute("SHOW COLUMNS FROM videos LIKE 'category_id'")
            if not cursor.fetchone():
                print("执行数据库迁移...")

                # 添加category_id字段
                cursor.execute("ALTER TABLE `videos` ADD COLUMN `category_id` int DEFAULT NULL")
                cursor.execute("ALTER TABLE `videos` ADD KEY `idx_category_id` (`category_id`)")

                # 为现有系列创建默认分类
                cursor.execute("""
                INSERT INTO `categories` (`series_id`, `title`, `description`, `price`, `order_index`)
                SELECT
                    `id` as `series_id`,
                    CONCAT(`title`, ' - 默认分类') as `title`,
                    '系统自动创建的默认分类' as `description`,
                    COALESCE(`price`, 0.00) as `price`,
                    1 as `order_index`
                FROM `series`
                WHERE NOT EXISTS (
                    SELECT 1 FROM `categories` WHERE `categories`.`series_id` = `series`.`id`
                )
                """)

                # 将现有视频关联到默认分类
                cursor.execute("""
                UPDATE `videos` v
                JOIN `categories` c ON c.series_id = v.series_id
                SET v.category_id = c.id
                WHERE v.category_id IS NULL
                AND c.title LIKE '%默认分类%'
                """)

                connection.commit()
                print("✅ 数据库迁移完成")

            cursor.close()
            connection.close()
        except Exception as e:
            print(f"数据库迁移失败: {e}")
    
    def connect_database(self):
        """连接数据库"""
        try:
            if self.db_connection and self.db_connection.connect():
                self.status_bar.showMessage("✅ 数据库连接成功")
                self.load_data()
            else:
                self.status_bar.showMessage("❌ 数据库连接失败")
                # 不显示警告对话框，只在状态栏显示错误
                print("数据库连接失败，请检查配置")
        except Exception as e:
            self.status_bar.showMessage(f"❌ 数据库连接错误: {str(e)}")
            print(f"数据库连接错误: {e}")
    
    def load_data(self):
        """加载数据"""
        if not self.db_connection or not self.db_connection.is_connected():
            self.status_bar.showMessage("❌ 数据库连接不可用")
            return

        # 使用线程加载数据
        self.data_loader = DataLoader(self.db_connection)
        self.data_loader.data_loaded.connect(self.update_overview)
        self.data_loader.error_occurred.connect(self.handle_data_error)
        self.data_loader.start()

        # 初始化课程数据（只在程序启动时执行一次）
        self.initialize_course_data()

    def initialize_course_data(self):
        """初始化课程数据（仅在程序启动时执行一次）"""
        try:
            # 防止重复初始化
            if hasattr(self, '_course_data_initialized') and self._course_data_initialized:
                print("📦 课程数据已初始化，跳过重复初始化")
                return

            print("🚀 开始初始化课程数据...")
            self._course_data_initialized = True

            # 开发阶段：强制API模式
            try:
                from config.api_config import api_config
                use_api = api_config.use_api  # 恢复API配置
                print(f"📡 API模式: {use_api} (开发阶段：强制使用服务器数据，无分页)")
            except:
                use_api = False
                print("📡 API模式: False (配置加载失败)")

            # 创建课程服务和用户服务
            from services.course_service import CourseService
            from services.user_service import UserService

            course_service = CourseService(self.db_manager, use_api=use_api, use_cache=True)
            user_service = UserService(self.db_manager, use_cache=True)

            # 一次性加载所有数据到内存
            try:
                print("🚀 开始一次性加载所有数据到内存...")
                from cache.global_data_manager import global_data_manager

                if global_data_manager.load_all_data_once():
                    print("✅ 所有数据已一次性加载到内存")
                    print("🚫 后续操作将完全使用内存数据，禁止网络请求")

                    # 显示数据统计
                    summary = global_data_manager.get_data_summary()
                    print(f"📊 内存数据统计: 系列={summary['series_count']}, 分类={summary['category_count']}, 视频={summary['video_count']}")
                else:
                    print("❌ 一次性数据加载失败，将使用降级模式")
            except Exception as e:
                print(f"⚠️ 一次性数据加载异常: {e}，将使用降级模式")

            print("🎉 数据加载完成")

        except Exception as e:
            print(f"❌ 初始化课程数据失败: {e}")

    def update_overview(self, data):
        """更新数据概览 - 安全版本"""
        try:
            # 检查是否在概览页面，如果不在就跳过更新
            if not hasattr(self, 'content_widget') or not self.content_widget:
                print("📊 不在概览页面，跳过概览数据更新")
                return

            # 检查概览页面的UI对象是否存在且有效
            labels_to_check = ['user_count_label', 'series_count_label', 'video_count_label', 'order_count_label']
            all_labels_valid = True

            for label_name in labels_to_check:
                if not hasattr(self, label_name):
                    all_labels_valid = False
                    break
                label = getattr(self, label_name)
                if not label or not hasattr(label, 'setText'):
                    all_labels_valid = False
                    break
                # 检查QLabel是否已被删除
                try:
                    label.text()  # 尝试访问text属性，如果对象被删除会抛出异常
                except RuntimeError:
                    all_labels_valid = False
                    break

            if all_labels_valid:
                # 安全更新UI
                self.user_count_label.setText(f"👥 用户数: {data['user_count']:,}")
                self.series_count_label.setText(f"📚 系列数: {data['series_count']:,}")
                self.video_count_label.setText(f"🎬 视频数: {data['video_count']:,}")
                self.order_count_label.setText(f"💰 订单数: {data['order_count']:,}")
                print("📊 概览数据更新成功")
            else:
                print("📊 概览页面UI对象无效，跳过更新")

            # 状态栏更新（相对安全）
            if hasattr(self, 'status_bar') and self.status_bar:
                try:
                    self.status_bar.showMessage("✅ 数据加载完成")
                except RuntimeError:
                    print("📊 状态栏对象无效，跳过状态更新")

        except Exception as e:
            print(f"📊 更新概览数据失败: {e}")
            # 不要因为概览更新失败而触发重新初始化
    
    def handle_data_error(self, error_msg):
        """处理数据加载错误"""
        self.status_bar.showMessage(f"❌ 数据加载失败: {error_msg}")
        QMessageBox.warning(self, "数据加载失败", f"无法加载数据: {error_msg}")
    
    def setup_timer(self):
        """设置定时器 - 优化版本"""
        # 每5分钟刷新一次数据（减少频率）
        self.timer = QTimer()
        self.timer.timeout.connect(self.safe_load_data)
        self.timer.start(300000)  # 5分钟

    def safe_load_data(self):
        """安全的数据加载 - 只在概览页面时加载"""
        try:
            # 只在概览页面时才加载数据
            current_widget = None
            if hasattr(self, 'content_layout') and self.content_layout.count() > 0:
                current_widget = self.content_layout.itemAt(0).widget()

            # 检查当前是否在概览页面
            is_overview_page = (
                current_widget and
                hasattr(current_widget, 'objectName') and
                'overview' in current_widget.objectName().lower()
            ) or not current_widget  # 如果没有widget，可能是概览页面

            if is_overview_page:
                print("📊 定时器：在概览页面，执行数据刷新")
                self.load_data()
            else:
                print("📊 定时器：不在概览页面，跳过数据刷新")

        except Exception as e:
            print(f"📊 定时器数据加载失败: {e}")

    def sync_from_server(self):
        """从服务端同步数据"""
        try:
            self.status_bar.showMessage("正在从服务端同步数据...")

            # 测试连接
            connection_result = self.sync_manager.test_connection()
            if not connection_result['success']:
                self.status_bar.showMessage(f"❌ {connection_result['message']}")
                return

            # 执行同步
            sync_result = self.sync_manager.sync_from_server()

            if sync_result['success']:
                self.status_bar.showMessage(f"✅ {sync_result['message']}")
                # 刷新界面数据
                if hasattr(self, 'course_window') and self.course_window:
                    self.course_window.refresh_all_data()
                # 刷新概览数据
                self.load_data()
            else:
                self.status_bar.showMessage(f"❌ {sync_result['message']}")

        except Exception as e:
            self.status_bar.showMessage(f"❌ 同步失败: {str(e)}")

    def sync_to_server(self, table_name: str, record_id: str, data: dict):
        """同步单条记录到服务端"""
        try:
            sync_result = self.sync_manager.sync_to_server(table_name, record_id, data)

            if sync_result['success']:
                self.status_bar.showMessage(f"✅ {sync_result['message']}")
            else:
                self.status_bar.showMessage(f"❌ {sync_result['message']}")

            return sync_result

        except Exception as e:
            error_msg = f"同步失败: {str(e)}"
            self.status_bar.showMessage(f"❌ {error_msg}")
            return {
                'success': False,
                'message': error_msg
            }


    def show_about(self):
        """显示关于信息"""
        QMessageBox.about(self, "关于", 
                         "水幕课程管理端 v1.0.0\n\n"
                         "一个用于管理水幕视频课程的桌面应用程序\n"
                         "技术栈: Python + PyQt6")
    
    def closeEvent(self, event):
        """窗口关闭事件"""
        if self.db_connection:
            self.db_connection.disconnect()
        event.accept()
