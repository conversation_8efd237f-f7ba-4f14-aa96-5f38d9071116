#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
课程管理窗口
"""

import sys
from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, 
                            QTableWidgetItem, QPushButton, QLineEdit, QComboBox,
                            QLabel, QMessageBox, QDialog, QFormLayout, QCheckBox,
                            QHeaderView, QAbstractItemView, QFrame, QGroupBox,
                            QTextEdit, QSpinBox, QProgressBar, QTabWidget,
                            QDoubleSpinBox)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt6.QtGui import QFont, QIcon
from services.course_service import CourseService
from database.models import DatabaseManager
from utils.exporters import ExcelExporter
from utils.settings import settings_manager
import logging

logger = logging.getLogger(__name__)

class CourseLoadThread(QThread):
    """课程数据加载线程"""
    data_loaded = pyqtSignal(dict)
    error_occurred = pyqtSignal(str)
    
    def __init__(self, course_service, data_type='series', page=1, page_size=20, search=None, filter_value=None):
        super().__init__()
        self.course_service = course_service
        self.data_type = data_type
        self.page = page
        self.page_size = page_size
        self.search = search
        self.filter_value = filter_value
    
    def run(self):
        try:
            if self.data_type == 'series':
                result = self.course_service.get_series_list(
                    page=self.page,
                    page_size=self.page_size,
                    search=self.search,
                    is_published=self.filter_value
                )
            else:  # videos
                result = self.course_service.get_video_list(
                    page=self.page,
                    page_size=self.page_size,
                    search=self.search,
                    series_id=self.filter_value
                )
            self.data_loaded.emit(result)
        except Exception as e:
            self.error_occurred.emit(str(e))

class SeriesEditDialog(QDialog):
    """系列编辑对话框"""
    
    def __init__(self, parent=None, series_data=None):
        super().__init__(parent)
        self.series_data = series_data
        self.init_ui()
        
        if series_data:
            self.load_series_data()
    
    def init_ui(self):
        self.setWindowTitle('编辑系列' if self.series_data else '新增系列')
        self.setFixedSize(500, 400)
        
        layout = QVBoxLayout()
        
        # 表单
        form_layout = QFormLayout()
        
        self.title_edit = QLineEdit()
        self.description_edit = QTextEdit()
        self.description_edit.setMaximumHeight(100)
        self.price_edit = QDoubleSpinBox()
        self.price_edit.setRange(0, 9999.99)
        self.price_edit.setDecimals(2)
        self.price_edit.setSuffix(' 元')
        self.is_published_check = QCheckBox()
        
        form_layout.addRow('系列标题*:', self.title_edit)
        form_layout.addRow('系列描述:', self.description_edit)
        form_layout.addRow('系列价格:', self.price_edit)
        form_layout.addRow('发布状态:', self.is_published_check)
        
        layout.addLayout(form_layout)
        
        # 按钮
        button_layout = QHBoxLayout()
        
        self.save_btn = QPushButton('保存')
        self.cancel_btn = QPushButton('取消')
        
        self.save_btn.clicked.connect(self.accept)
        self.cancel_btn.clicked.connect(self.reject)
        
        button_layout.addWidget(self.save_btn)
        button_layout.addWidget(self.cancel_btn)
        
        layout.addLayout(button_layout)
        
        self.setLayout(layout)
    
    def load_series_data(self):
        """加载系列数据"""
        self.title_edit.setText(self.series_data.get('title', ''))
        self.description_edit.setText(self.series_data.get('description', ''))
        self.price_edit.setValue(self.series_data.get('price', 0.0))
        self.is_published_check.setChecked(self.series_data.get('is_published', False))
    
    def get_form_data(self):
        """获取表单数据"""
        return {
            'title': self.title_edit.text().strip(),
            'description': self.description_edit.toPlainText().strip(),
            'price': self.price_edit.value(),
            'is_published': self.is_published_check.isChecked()
        }

class VideoEditDialog(QDialog):
    """视频编辑对话框"""
    
    def __init__(self, parent=None, video_data=None, series_list=None):
        super().__init__(parent)
        self.video_data = video_data
        self.series_list = series_list or []
        self.init_ui()
        
        if video_data:
            self.load_video_data()
    
    def init_ui(self):
        self.setWindowTitle('编辑视频' if self.video_data else '新增视频')
        self.setFixedSize(500, 450)
        
        layout = QVBoxLayout()
        
        # 表单
        form_layout = QFormLayout()
        
        self.title_edit = QLineEdit()
        self.description_edit = QTextEdit()
        self.description_edit.setMaximumHeight(80)
        
        self.series_combo = QComboBox()
        self.series_combo.addItem('无关联系列', None)
        for series in self.series_list:
            self.series_combo.addItem(series['title'], series['id'])
        
        self.video_url_edit = QLineEdit()
        self.duration_edit = QSpinBox()
        self.duration_edit.setRange(0, 999999)
        self.duration_edit.setSuffix(' 秒')
        self.order_index_edit = QSpinBox()
        self.order_index_edit.setRange(0, 9999)
        
        form_layout.addRow('视频标题*:', self.title_edit)
        form_layout.addRow('视频描述:', self.description_edit)
        form_layout.addRow('所属系列:', self.series_combo)
        form_layout.addRow('视频地址:', self.video_url_edit)
        form_layout.addRow('视频时长:', self.duration_edit)
        form_layout.addRow('排序序号:', self.order_index_edit)
        
        layout.addLayout(form_layout)
        
        # 按钮
        button_layout = QHBoxLayout()
        
        self.save_btn = QPushButton('保存')
        self.cancel_btn = QPushButton('取消')
        
        self.save_btn.clicked.connect(self.accept)
        self.cancel_btn.clicked.connect(self.reject)
        
        button_layout.addWidget(self.save_btn)
        button_layout.addWidget(self.cancel_btn)
        
        layout.addLayout(button_layout)
        
        self.setLayout(layout)
    
    def load_video_data(self):
        """加载视频数据"""
        self.title_edit.setText(self.video_data.get('title', ''))
        self.description_edit.setText(self.video_data.get('description', ''))
        self.video_url_edit.setText(self.video_data.get('video_url', ''))
        self.duration_edit.setValue(self.video_data.get('duration', 0))
        self.order_index_edit.setValue(self.video_data.get('order_index', 0))
        
        # 设置系列选择
        series_id = self.video_data.get('series_id')
        if series_id:
            for i in range(self.series_combo.count()):
                if self.series_combo.itemData(i) == series_id:
                    self.series_combo.setCurrentIndex(i)
                    break
    
    def get_form_data(self):
        """获取表单数据"""
        data = {
            'title': self.title_edit.text().strip(),
            'description': self.description_edit.toPlainText().strip(),
            'video_url': self.video_url_edit.text().strip(),
            'duration': self.duration_edit.value(),
            'order_index': self.order_index_edit.value()
        }
        
        # 获取选中的系列ID
        series_id = self.series_combo.currentData()
        if series_id:
            data['series_id'] = series_id
        
        return data

class CategoryFormDialog(QDialog):
    """分类表单对话框"""

    def __init__(self, course_service, category_id=None, parent=None):
        super().__init__(parent)
        self.course_service = course_service
        self.category_id = category_id
        self.is_edit = category_id is not None

        self.init_ui()
        self.load_series()

        if self.is_edit:
            self.load_category_data()

    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle('编辑分类' if self.is_edit else '新增分类')
        self.setFixedSize(500, 400)

        layout = QVBoxLayout()

        # 表单
        form_layout = QFormLayout()

        # 所属系列
        self.series_combo = QComboBox()
        form_layout.addRow('所属系列:', self.series_combo)

        # 分类标题
        self.title_edit = QLineEdit()
        self.title_edit.setPlaceholderText('请输入分类标题')
        form_layout.addRow('分类标题:', self.title_edit)

        # 分类描述
        self.description_edit = QTextEdit()
        self.description_edit.setPlaceholderText('请输入分类描述')
        self.description_edit.setMaximumHeight(100)
        form_layout.addRow('分类描述:', self.description_edit)

        # 分类价格
        self.price_edit = QDoubleSpinBox()
        self.price_edit.setRange(0, 99999.99)
        self.price_edit.setDecimals(2)
        self.price_edit.setSuffix(' 元')
        form_layout.addRow('分类价格:', self.price_edit)

        # 排序序号
        self.order_index_edit = QSpinBox()
        self.order_index_edit.setRange(0, 9999)
        self.order_index_edit.setValue(1)
        form_layout.addRow('排序序号:', self.order_index_edit)

        layout.addLayout(form_layout)

        # 按钮
        button_layout = QHBoxLayout()

        self.save_btn = QPushButton('保存')
        self.cancel_btn = QPushButton('取消')

        self.save_btn.clicked.connect(self.accept)
        self.cancel_btn.clicked.connect(self.reject)

        button_layout.addWidget(self.save_btn)
        button_layout.addWidget(self.cancel_btn)

        layout.addLayout(button_layout)

        self.setLayout(layout)

    def load_series(self):
        """加载系列列表"""
        try:
            result = self.course_service.get_series_list(page=1, page_size=100)
            if result['success']:
                self.series_combo.clear()

                for series in result['data']:
                    self.series_combo.addItem(series['title'], series['id'])
        except Exception as e:
            QMessageBox.critical(self, '错误', f"加载系列列表失败: {str(e)}")

    def load_category_data(self):
        """加载分类数据（编辑模式）"""
        try:
            result = self.course_service.get_category_detail(self.category_id)
            if result['success']:
                category = result['data']

                self.title_edit.setText(category['title'])
                self.description_edit.setPlainText(category.get('description', ''))

                # 处理价格字段，API返回的可能是price而不是price
                price = category.get('price', 0)
                if price is None:
                    price = 0
                self.price_edit.setValue(float(price))

                # 处理order_index字段，API可能不返回此字段
                order_index = category.get('order_index', 0)
                if order_index is None:
                    order_index = 0
                self.order_index_edit.setValue(int(order_index))

                # 设置系列选择，处理seriesId字段名
                series_id = category.get('series_id') or category.get('seriesId')
                if series_id:
                    for i in range(self.series_combo.count()):
                        if self.series_combo.itemData(i) == series_id:
                            self.series_combo.setCurrentIndex(i)
                            break
            else:
                QMessageBox.critical(self, '错误', f"加载分类数据失败: {result['message']}")
        except Exception as e:
            QMessageBox.critical(self, '错误', f"加载分类数据失败: {str(e)}")

    def get_form_data(self):
        """获取表单数据"""
        data = {
            'series_id': self.series_combo.currentData(),
            'title': self.title_edit.text().strip(),
            'description': self.description_edit.toPlainText().strip(),
            'price': self.price_edit.value(),
            'order_index': self.order_index_edit.value()
        }

        return data

class VideoEditDialog(QDialog):
    """视频编辑对话框"""

    def __init__(self, parent=None, video_data=None, series_list=None):
        super().__init__(parent)
        self.video_data = video_data
        self.series_list = series_list or []
        self.is_edit = video_data is not None

        self.init_ui()
        self.load_categories()

        if self.is_edit:
            self.load_video_data()

    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle('编辑视频' if self.is_edit else '新增视频')
        self.setFixedSize(500, 450)

        layout = QVBoxLayout()

        # 表单
        form_layout = QFormLayout()

        # 所属系列
        self.series_combo = QComboBox()
        self.series_combo.currentTextChanged.connect(self.on_series_changed)
        form_layout.addRow('所属系列:', self.series_combo)

        # 所属分类
        self.category_combo = QComboBox()
        form_layout.addRow('所属分类:', self.category_combo)

        # 视频标题
        self.title_edit = QLineEdit()
        self.title_edit.setPlaceholderText('请输入视频标题')
        form_layout.addRow('视频标题:', self.title_edit)

        # 视频描述
        self.description_edit = QTextEdit()
        self.description_edit.setPlaceholderText('请输入视频描述')
        self.description_edit.setMaximumHeight(80)
        form_layout.addRow('视频描述:', self.description_edit)

        # 视频URL
        self.video_url_edit = QLineEdit()
        self.video_url_edit.setPlaceholderText('请输入视频URL')
        form_layout.addRow('视频URL:', self.video_url_edit)

        # 视频时长
        self.duration_edit = QSpinBox()
        self.duration_edit.setRange(0, 99999)
        self.duration_edit.setSuffix(' 秒')
        form_layout.addRow('视频时长:', self.duration_edit)

        # 排序序号
        self.order_index_edit = QSpinBox()
        self.order_index_edit.setRange(0, 9999)
        self.order_index_edit.setValue(1)
        form_layout.addRow('排序序号:', self.order_index_edit)

        layout.addLayout(form_layout)

        # 按钮
        button_layout = QHBoxLayout()

        self.save_btn = QPushButton('保存')
        self.cancel_btn = QPushButton('取消')

        self.save_btn.clicked.connect(self.accept)
        self.cancel_btn.clicked.connect(self.reject)

        button_layout.addWidget(self.save_btn)
        button_layout.addWidget(self.cancel_btn)

        layout.addLayout(button_layout)

        self.setLayout(layout)

    def load_categories(self):
        """加载系列和分类列表"""
        # 加载系列
        self.series_combo.clear()
        for series in self.series_list:
            self.series_combo.addItem(series['title'], series['id'])

    def on_series_changed(self):
        """系列选择改变时加载分类"""
        series_id = self.series_combo.currentData()
        self.category_combo.clear()

        if series_id:
            # 这里需要从父窗口获取course_service
            try:
                parent_window = self.parent()
                if hasattr(parent_window, 'course_service'):
                    # 使用QTimer延迟执行，避免卡顿
                    QTimer.singleShot(100, lambda: self._load_categories_for_series(series_id))
            except Exception as e:
                print(f"加载分类失败: {e}")

    def _load_categories_for_series(self, series_id):
        """为指定系列加载分类"""
        try:
            parent_window = self.parent()
            if hasattr(parent_window, 'course_service'):
                result = parent_window.course_service.get_category_list(page=1, page_size=100, series_id=series_id)
                if result['success']:
                    for category in result['data']:
                        self.category_combo.addItem(category['title'], category['id'])
        except Exception as e:
            print(f"加载分类失败: {e}")

    def load_video_data(self):
        """加载视频数据（编辑模式）"""
        if self.video_data:
            self.title_edit.setText(self.video_data.get('title', ''))
            self.description_edit.setPlainText(self.video_data.get('description', ''))
            self.video_url_edit.setText(self.video_data.get('video_url', ''))
            self.duration_edit.setValue(self.video_data.get('duration', 0))
            self.order_index_edit.setValue(self.video_data.get('order_index', 1))

            # 设置系列选择
            series_id = self.video_data.get('series_id')
            if series_id:
                for i in range(self.series_combo.count()):
                    if self.series_combo.itemData(i) == series_id:
                        self.series_combo.setCurrentIndex(i)
                        self.on_series_changed()  # 加载分类
                        break

            # 设置分类选择
            category_id = self.video_data.get('category_id')
            if category_id:
                for i in range(self.category_combo.count()):
                    if self.category_combo.itemData(i) == category_id:
                        self.category_combo.setCurrentIndex(i)
                        break

    def get_form_data(self):
        """获取表单数据"""
        data = {
            'category_id': self.category_combo.currentData(),
            'title': self.title_edit.text().strip(),
            'description': self.description_edit.toPlainText().strip(),
            'video_url': self.video_url_edit.text().strip(),
            'duration': self.duration_edit.value(),
            'order_index': self.order_index_edit.value()
        }

        return data

class SeriesEditDialog(QDialog):
    """系列编辑对话框"""

    def __init__(self, parent=None, series_data=None):
        super().__init__(parent)
        self.series_data = series_data
        self.is_edit = series_data is not None

        self.init_ui()

        if self.is_edit:
            self.load_series_data()

    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle('编辑系列' if self.is_edit else '新增系列')
        self.setFixedSize(500, 350)

        layout = QVBoxLayout()

        # 表单
        form_layout = QFormLayout()

        # 系列标题
        self.title_edit = QLineEdit()
        self.title_edit.setPlaceholderText('请输入系列标题')
        form_layout.addRow('系列标题:', self.title_edit)

        # 系列描述
        self.description_edit = QTextEdit()
        self.description_edit.setPlaceholderText('请输入系列描述')
        self.description_edit.setMaximumHeight(100)
        form_layout.addRow('系列描述:', self.description_edit)

        # 发布状态
        self.is_published_checkbox = QCheckBox('发布系列')
        form_layout.addRow('发布状态:', self.is_published_checkbox)

        layout.addLayout(form_layout)

        # 按钮
        button_layout = QHBoxLayout()

        self.save_btn = QPushButton('保存')
        self.cancel_btn = QPushButton('取消')

        self.save_btn.clicked.connect(self.accept)
        self.cancel_btn.clicked.connect(self.reject)

        button_layout.addWidget(self.save_btn)
        button_layout.addWidget(self.cancel_btn)

        layout.addLayout(button_layout)

        self.setLayout(layout)

    def load_series_data(self):
        """加载系列数据（编辑模式）"""
        if self.series_data:
            self.title_edit.setText(self.series_data.get('title', ''))
            self.description_edit.setPlainText(self.series_data.get('description', ''))
            self.is_published_checkbox.setChecked(self.series_data.get('is_published', False))

    def get_form_data(self):
        """获取表单数据"""
        data = {
            'title': self.title_edit.text().strip(),
            'description': self.description_edit.toPlainText().strip(),
            'is_published': self.is_published_checkbox.isChecked()
        }

        return data

class CourseManagementWindow(QWidget):
    """课程管理窗口"""
    
    def __init__(self, db_manager: DatabaseManager, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager

        # 检查是否使用API模式
        try:
            from config.api_config import api_config
            # 开发阶段：强制API模式
            use_api = api_config.use_api
            print(f"🔧 API配置: use_api={use_api} (开发阶段：强制使用服务器数据，API无分页)")
        except Exception as e:
            use_api = False
            print(f"❌ API配置加载失败: {e}")

        self.course_service = CourseService(db_manager, use_api=use_api, use_cache=True)
        print(f"📊 CourseService初始化完成，API模式: {self.course_service.use_api}，缓存模式: {self.course_service.use_cache}")
        self.load_thread = None
        
        # 系列管理相关
        self.series_current_page = 1
        self.series_page_size = 20
        self.series_total_pages = 1
        
        # 分类管理相关
        self.category_current_page = 1
        self.category_page_size = 100  # 增大页面大小，确保获取所有数据
        self.category_total_pages = 1

        # 视频管理相关
        self.video_current_page = 1
        self.video_page_size = 100  # 增大页面大小，确保获取所有数据
        self.video_total_pages = 1
        
        self.init_ui()

        # 使用QTimer延迟加载数据，避免界面卡顿（使用缓存数据，不重复加载）
        QTimer.singleShot(100, self.load_initial_data)

    def load_initial_data(self):
        """延迟加载初始数据 - 开发阶段强制从服务端获取"""
        try:
            print("🚀 开始加载课程管理初始数据（开发阶段：强制从服务端获取）...")

            # 开发阶段：清除所有界面缓存，强制重新加载
            if hasattr(self, '_cached_series_data'):
                delattr(self, '_cached_series_data')
            if hasattr(self, '_videos_loaded'):
                delattr(self, '_videos_loaded')
            if hasattr(self, '_series_loaded'):
                delattr(self, '_series_loaded')
            if hasattr(self, '_categories_loaded'):
                delattr(self, '_categories_loaded')

            current_tab = self.tab_widget.currentIndex()
            print(f"📍 当前标签页索引: {current_tab}")

            # 强制加载当前标签页的数据
            if current_tab == 0:  # 视频管理
                print("📹 强制加载视频管理数据...")
                self.load_videos()
                print("✅ 视频管理数据加载完成")
            elif current_tab == 1:  # 系列管理
                print("📚 强制加载系列管理数据...")
                self.load_series()
                print("✅ 系列管理数据加载完成")
            elif current_tab == 2:  # 分类管理
                print("📂 强制加载分类管理数据...")
                self.load_categories()
                print("✅ 分类管理数据加载完成")

            # 强制更新筛选下拉框
            self.update_video_series_filter()
            self.load_series_for_category_filter()

            print("🎉 课程管理初始数据加载完成（缓存模式）")
        except Exception as e:
            print(f"❌ 加载初始数据失败: {e}")

    def on_tab_changed(self, index):
        """标签页切换事件 - 缓存优化版本，避免重复加载"""
        try:
            print(f"🔄 切换到标签页 {index}")

            # 开发阶段：禁用缓存机制，强制重新加载数据
            if index == 0:  # 视频管理
                print("📹 开发阶段：强制重新加载视频管理数据...")
                self.load_videos()

            elif index == 1:  # 系列管理
                print("📚 开发阶段：强制重新加载系列管理数据...")
                self.load_series()

            elif index == 2:  # 分类管理
                print("📂 开发阶段：强制重新加载分类管理数据...")
                self.load_categories()

            print(f"✅ 标签页 {index} 切换完成（开发阶段：强制API模式）")
        except Exception as e:
            print(f"❌ 切换标签页失败: {e}")

    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout()
        
        # 页面标题
        title = QLabel("📚 课程管理")
        title.setStyleSheet("""
            QLabel {
                font-size: 20px;
                font-weight: bold;
                padding: 15px 0;
                color: #333;
            }
        """)
        layout.addWidget(title)
        
        # 创建标签页
        self.tab_widget = QTabWidget()

        # 视频管理标签页（放在最左边，默认显示）
        self.video_tab = QWidget()
        self.setup_video_tab()
        self.tab_widget.addTab(self.video_tab, "视频管理")

        # 系列管理标签页
        self.series_tab = QWidget()
        self.setup_series_tab()
        self.tab_widget.addTab(self.series_tab, "系列管理")

        # 分类管理标签页
        self.category_tab = QWidget()
        self.setup_category_tab()
        self.tab_widget.addTab(self.category_tab, "分类管理")
        
        # 标签页切换事件
        self.tab_widget.currentChanged.connect(self.on_tab_changed)
        
        layout.addWidget(self.tab_widget)
        
        self.setLayout(layout)
    
    def setup_series_tab(self):
        """设置系列管理标签页"""
        layout = QVBoxLayout()
        
        # 工具栏
        toolbar_layout = QHBoxLayout()
        
        # 搜索框
        self.series_search_edit = QLineEdit()
        self.series_search_edit.setPlaceholderText('搜索系列标题或描述...')
        self.series_search_edit.returnPressed.connect(self.search_series)
        
        # 状态筛选
        self.series_status_combo = QComboBox()
        self.series_status_combo.addItems(['全部', '已发布', '未发布'])
        self.series_status_combo.currentTextChanged.connect(self.filter_series)
        
        # 按钮
        self.series_search_btn = QPushButton('搜索')
        self.add_series_btn = QPushButton('新增系列')
        self.refresh_series_btn = QPushButton('刷新')
        self.export_series_btn = QPushButton('导出Excel')
        
        self.series_search_btn.clicked.connect(self.search_series)
        self.add_series_btn.clicked.connect(self.add_series)
        self.refresh_series_btn.clicked.connect(self.refresh_series)
        self.export_series_btn.clicked.connect(self.export_series)
        
        toolbar_layout.addWidget(QLabel('搜索:'))
        toolbar_layout.addWidget(self.series_search_edit)
        toolbar_layout.addWidget(QLabel('状态:'))
        toolbar_layout.addWidget(self.series_status_combo)
        toolbar_layout.addWidget(self.series_search_btn)
        toolbar_layout.addStretch()
        toolbar_layout.addWidget(self.add_series_btn)
        toolbar_layout.addWidget(self.refresh_series_btn)
        toolbar_layout.addWidget(self.export_series_btn)
        
        layout.addLayout(toolbar_layout)
        
        # 系列表格
        self.series_table = QTableWidget()
        self.setup_series_table()
        layout.addWidget(self.series_table)
        
        # 分页控件
        series_pagination_layout = QHBoxLayout()
        
        self.series_prev_btn = QPushButton('上一页')
        self.series_next_btn = QPushButton('下一页')
        self.series_page_label = QLabel('第 1 页，共 1 页')
        self.series_page_size_combo = QComboBox()
        self.series_page_size_combo.addItems(['10', '20', '50', '100'])
        self.series_page_size_combo.setCurrentText('20')
        self.series_page_size_combo.currentTextChanged.connect(self.change_series_page_size)
        
        self.series_prev_btn.clicked.connect(self.series_prev_page)
        self.series_next_btn.clicked.connect(self.series_next_page)
        
        series_pagination_layout.addWidget(self.series_prev_btn)
        series_pagination_layout.addWidget(self.series_next_btn)
        series_pagination_layout.addStretch()
        series_pagination_layout.addWidget(self.series_page_label)
        series_pagination_layout.addStretch()
        series_pagination_layout.addWidget(QLabel('每页显示:'))
        series_pagination_layout.addWidget(self.series_page_size_combo)
        
        layout.addLayout(series_pagination_layout)
        
        # 加载进度条
        self.series_progress_bar = QProgressBar()
        self.series_progress_bar.setVisible(False)
        layout.addWidget(self.series_progress_bar)
        
        self.series_tab.setLayout(layout)
    
    def setup_video_tab(self):
        """设置视频管理标签页"""
        layout = QVBoxLayout()
        
        # 工具栏
        toolbar_layout = QHBoxLayout()
        
        # 搜索框
        self.video_search_edit = QLineEdit()
        self.video_search_edit.setPlaceholderText('搜索视频标题或描述...')
        self.video_search_edit.returnPressed.connect(self.search_videos)
        
        # 系列筛选
        self.video_series_combo = QComboBox()
        self.video_series_combo.addItem('全部系列', None)
        self.video_series_combo.currentTextChanged.connect(self.filter_videos)
        
        # 按钮
        self.video_search_btn = QPushButton('搜索')
        self.add_video_btn = QPushButton('新增视频')
        self.batch_import_btn = QPushButton('批量导入')
        self.refresh_video_btn = QPushButton('刷新')
        self.export_video_btn = QPushButton('导出Excel')
        
        self.video_search_btn.clicked.connect(self.search_videos)
        self.add_video_btn.clicked.connect(self.add_video)
        self.batch_import_btn.clicked.connect(self.batch_import_videos)
        self.refresh_video_btn.clicked.connect(self.refresh_videos)
        self.export_video_btn.clicked.connect(self.export_videos)
        
        toolbar_layout.addWidget(QLabel('搜索:'))
        toolbar_layout.addWidget(self.video_search_edit)
        toolbar_layout.addWidget(QLabel('系列:'))
        toolbar_layout.addWidget(self.video_series_combo)
        toolbar_layout.addWidget(self.video_search_btn)
        toolbar_layout.addStretch()
        toolbar_layout.addWidget(self.add_video_btn)
        toolbar_layout.addWidget(self.batch_import_btn)
        toolbar_layout.addWidget(self.refresh_video_btn)
        toolbar_layout.addWidget(self.export_video_btn)
        
        layout.addLayout(toolbar_layout)
        
        # 视频表格
        self.video_table = QTableWidget()
        self.setup_video_table()
        layout.addWidget(self.video_table)
        
        # 分页控件
        video_pagination_layout = QHBoxLayout()
        
        self.video_prev_btn = QPushButton('上一页')
        self.video_next_btn = QPushButton('下一页')
        self.video_page_label = QLabel('第 1 页，共 1 页')
        self.video_page_size_combo = QComboBox()
        self.video_page_size_combo.addItems(['10', '20', '50', '100'])
        self.video_page_size_combo.setCurrentText('20')
        self.video_page_size_combo.currentTextChanged.connect(self.change_video_page_size)
        
        self.video_prev_btn.clicked.connect(self.video_prev_page)
        self.video_next_btn.clicked.connect(self.video_next_page)
        
        video_pagination_layout.addWidget(self.video_prev_btn)
        video_pagination_layout.addWidget(self.video_next_btn)
        video_pagination_layout.addStretch()
        video_pagination_layout.addWidget(self.video_page_label)
        video_pagination_layout.addStretch()
        video_pagination_layout.addWidget(QLabel('每页显示:'))
        video_pagination_layout.addWidget(self.video_page_size_combo)
        
        layout.addLayout(video_pagination_layout)
        
        # 加载进度条
        self.video_progress_bar = QProgressBar()
        self.video_progress_bar.setVisible(False)
        layout.addWidget(self.video_progress_bar)
        
        self.video_tab.setLayout(layout)

    def setup_category_tab(self):
        """设置分类管理标签页"""
        layout = QVBoxLayout()

        # 工具栏
        toolbar_layout = QHBoxLayout()

        # 搜索框
        self.category_search_edit = QLineEdit()
        self.category_search_edit.setPlaceholderText('搜索分类标题...')

        # 系列筛选
        self.category_series_combo = QComboBox()
        self.category_series_combo.addItem('全部系列', None)
        self.category_series_combo.currentTextChanged.connect(self.filter_categories)

        # 按钮
        self.category_search_btn = QPushButton('搜索')
        self.add_category_btn = QPushButton('新增分类')
        self.refresh_category_btn = QPushButton('刷新')

        self.category_search_btn.clicked.connect(self.search_categories)
        self.add_category_btn.clicked.connect(self.add_category)
        self.refresh_category_btn.clicked.connect(self.refresh_categories)

        toolbar_layout.addWidget(QLabel('搜索:'))
        toolbar_layout.addWidget(self.category_search_edit)
        toolbar_layout.addWidget(QLabel('系列:'))
        toolbar_layout.addWidget(self.category_series_combo)
        toolbar_layout.addWidget(self.category_search_btn)
        toolbar_layout.addStretch()
        toolbar_layout.addWidget(self.add_category_btn)
        toolbar_layout.addWidget(self.refresh_category_btn)

        layout.addLayout(toolbar_layout)

        # 分类表格
        self.category_table = QTableWidget()
        self.setup_category_table()
        layout.addWidget(self.category_table)

        # 分页控件
        category_pagination_layout = QHBoxLayout()

        self.category_prev_btn = QPushButton('上一页')
        self.category_next_btn = QPushButton('下一页')
        self.category_page_label = QLabel('第 1 页，共 1 页')
        self.category_page_size_combo = QComboBox()
        self.category_page_size_combo.addItems(['10', '20', '50', '100'])
        self.category_page_size_combo.setCurrentText('20')

        self.category_prev_btn.clicked.connect(self.prev_category_page)
        self.category_next_btn.clicked.connect(self.next_category_page)
        self.category_page_size_combo.currentTextChanged.connect(self.change_category_page_size)

        category_pagination_layout.addWidget(self.category_prev_btn)
        category_pagination_layout.addWidget(self.category_next_btn)
        category_pagination_layout.addWidget(self.category_page_label)
        category_pagination_layout.addStretch()
        category_pagination_layout.addWidget(QLabel('每页显示:'))
        category_pagination_layout.addWidget(self.category_page_size_combo)

        layout.addLayout(category_pagination_layout)

        # 加载进度条
        self.category_progress_bar = QProgressBar()
        self.category_progress_bar.setVisible(False)
        layout.addWidget(self.category_progress_bar)

        self.category_tab.setLayout(layout)

    def setup_series_table(self):
        """设置系列表格"""
        headers = ['ID', '系列标题', '描述', '总价格', '分类数', '视频数', '发布状态', '创建时间', '操作']
        self.series_table.setColumnCount(len(headers))
        self.series_table.setHorizontalHeaderLabels(headers)

        # 设置表格属性
        self.series_table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.series_table.setAlternatingRowColors(True)

        # 设置列宽 - 允许手动调整
        header = self.series_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.Interactive)  # 允许手动调整所有列

        # 设置默认列宽
        default_widths = [60, 200, 250, 100, 80, 80, 100, 150, 250]

        # 尝试从设置中恢复列宽
        saved_widths = settings_manager.get_column_widths("series_table")
        if saved_widths and len(saved_widths) == len(default_widths):
            for i, width in enumerate(saved_widths):
                self.series_table.setColumnWidth(i, width)
        else:
            # 使用默认列宽
            for i, width in enumerate(default_widths):
                self.series_table.setColumnWidth(i, width)

        # 连接列宽变化信号
        header.sectionResized.connect(self.save_series_table_column_widths)

    def setup_category_table(self):
        """设置分类表格"""
        headers = ['ID', '分类标题', '所属系列', '分类价格', '视频数量', '总时长', '排序', '创建时间', '操作']
        self.category_table.setColumnCount(len(headers))
        self.category_table.setHorizontalHeaderLabels(headers)

        # 设置表格属性
        self.category_table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.category_table.setAlternatingRowColors(True)
        self.category_table.setSortingEnabled(True)

        # 设置列宽 - 允许手动调整
        header = self.category_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.Interactive)  # 允许手动调整所有列

        # 设置默认列宽
        default_widths = [60, 200, 150, 100, 80, 100, 80, 150, 200]

        # 尝试从设置中恢复列宽
        saved_widths = settings_manager.get_column_widths("category_table")
        if saved_widths and len(saved_widths) == len(default_widths):
            for i, width in enumerate(saved_widths):
                self.category_table.setColumnWidth(i, width)
        else:
            # 使用默认列宽
            for i, width in enumerate(default_widths):
                self.category_table.setColumnWidth(i, width)

        # 连接列宽变化信号
        header.sectionResized.connect(self.save_category_table_column_widths)

    def setup_video_table(self):
        """设置视频表格"""
        headers = ['ID', '视频标题', '所属分类', '所属系列', '分类价格', '时长', '排序', '创建时间', '操作']
        self.video_table.setColumnCount(len(headers))
        self.video_table.setHorizontalHeaderLabels(headers)

        # 设置表格属性
        self.video_table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.video_table.setAlternatingRowColors(True)

        # 设置列宽 - 允许手动调整
        header = self.video_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.Interactive)  # 允许手动调整所有列

        # 设置默认列宽
        default_widths = [60, 200, 120, 120, 100, 80, 60, 150, 200]

        # 尝试从设置中恢复列宽
        saved_widths = settings_manager.get_column_widths("video_table")
        if saved_widths and len(saved_widths) == len(default_widths):
            for i, width in enumerate(saved_widths):
                self.video_table.setColumnWidth(i, width)
        else:
            # 使用默认列宽
            for i, width in enumerate(default_widths):
                self.video_table.setColumnWidth(i, width)

        # 连接列宽变化信号
        header.sectionResized.connect(self.save_video_table_column_widths)

    def save_video_table_column_widths(self):
        """保存视频表格列宽"""
        try:
            widths = []
            for i in range(self.video_table.columnCount()):
                widths.append(self.video_table.columnWidth(i))
            settings_manager.set_column_widths("video_table", widths)
            settings_manager.save_settings()
        except Exception as e:
            print(f"保存视频表格列宽失败: {e}")

    def save_series_table_column_widths(self):
        """保存系列表格列宽"""
        try:
            widths = []
            for i in range(self.series_table.columnCount()):
                widths.append(self.series_table.columnWidth(i))
            settings_manager.set_column_widths("series_table", widths)
            settings_manager.save_settings()
        except Exception as e:
            print(f"保存系列表格列宽失败: {e}")

    def save_category_table_column_widths(self):
        """保存分类表格列宽"""
        try:
            widths = []
            for i in range(self.category_table.columnCount()):
                widths.append(self.category_table.columnWidth(i))
            settings_manager.set_column_widths("category_table", widths)
            settings_manager.save_settings()
        except Exception as e:
            print(f"保存分类表格列宽失败: {e}")

    # ==================== 系列管理方法 ====================

    def load_series(self):
        """加载系列数据"""
        if self.load_thread and self.load_thread.isRunning():
            return

        self.series_progress_bar.setVisible(True)
        self.series_progress_bar.setRange(0, 0)

        # 获取筛选条件
        search = self.series_search_edit.text().strip() or None
        status_text = self.series_status_combo.currentText()
        is_published = None if status_text == '全部' else (status_text == '已发布')

        self.load_thread = CourseLoadThread(
            self.course_service,
            'series',
            self.series_current_page,
            self.series_page_size,
            search,
            is_published
        )
        self.load_thread.data_loaded.connect(self.on_series_loaded)
        self.load_thread.error_occurred.connect(self.on_load_error)
        self.load_thread.start()

    def on_series_loaded(self, result):
        """系列数据加载完成"""
        self.series_progress_bar.setVisible(False)

        if result['success']:
            # 缓存系列数据，供分类筛选使用
            self._cached_series_data = result['data']
            print(f"💾 缓存系列数据: {len(result['data'])} 条")

            # 如果分类筛选下拉框还没有填充，现在填充
            if not hasattr(self, '_category_series_combo_loaded'):
                self.fill_category_series_combo()

            self.populate_series_table(result['data'])
            self.update_series_pagination(result['pagination'])
        else:
            QMessageBox.warning(self, '错误', result['message'])

    def populate_series_table(self, series_list):
        """填充系列表格数据"""
        self.series_table.setRowCount(len(series_list))

        for row, series in enumerate(series_list):
            # ID
            self.series_table.setItem(row, 0, QTableWidgetItem(str(series['id'])))

            # 系列标题
            self.series_table.setItem(row, 1, QTableWidgetItem(series['title']))

            # 描述
            description = series.get('description', '')[:50] + '...' if len(series.get('description', '')) > 50 else series.get('description', '')
            self.series_table.setItem(row, 2, QTableWidgetItem(description))

            # 总价格（动态计算）
            price_item = QTableWidgetItem(f"¥{series['price']:.2f}")
            price_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.series_table.setItem(row, 3, price_item)

            # 分类数量
            category_count = series.get('category_count', 0)
            category_item = QTableWidgetItem(str(category_count))
            category_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.series_table.setItem(row, 4, category_item)

            # 视频数量
            video_count = series.get('video_count', 0)  # 默认为0，避免KeyError
            video_item = QTableWidgetItem(str(video_count))
            video_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.series_table.setItem(row, 5, video_item)

            # 发布状态
            is_published = series.get('is_published', True)  # 默认为已发布
            status_text = '已发布' if is_published else '未发布'
            status_item = QTableWidgetItem(status_text)
            status_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            if is_published:
                status_item.setBackground(Qt.GlobalColor.green)
            else:
                status_item.setBackground(Qt.GlobalColor.yellow)
            self.series_table.setItem(row, 6, status_item)

            # 创建时间
            created_at = series.get('created_at', '')
            created_at = created_at[:19] if created_at else ''
            self.series_table.setItem(row, 7, QTableWidgetItem(created_at))

            # 操作按钮
            self.create_series_action_buttons(row, series)

    def create_series_action_buttons(self, row, series):
        """创建系列操作按钮"""
        widget = QWidget()
        layout = QHBoxLayout()
        layout.setContentsMargins(5, 2, 5, 2)

        # 查看按钮
        view_btn = QPushButton('查看')
        view_btn.setFixedSize(50, 25)
        view_btn.clicked.connect(lambda: self.view_series(series['id']))

        # 编辑按钮
        edit_btn = QPushButton('编辑')
        edit_btn.setFixedSize(50, 25)
        edit_btn.clicked.connect(lambda: self.edit_series(series))

        # 发布/下架按钮
        is_published = series.get('is_published', True)
        publish_text = '下架' if is_published else '发布'
        publish_btn = QPushButton(publish_text)
        publish_btn.setFixedSize(50, 25)
        publish_btn.clicked.connect(lambda: self.toggle_series_publish(series['id'], series.get('title', '')))

        # 删除按钮
        delete_btn = QPushButton('删除')
        delete_btn.setFixedSize(50, 25)
        delete_btn.clicked.connect(lambda: self.delete_series(series['id'], series.get('title', '')))

        layout.addWidget(view_btn)
        layout.addWidget(edit_btn)
        layout.addWidget(publish_btn)
        layout.addWidget(delete_btn)

        widget.setLayout(layout)
        self.series_table.setCellWidget(row, 8, widget)

    def update_series_pagination(self, pagination):
        """更新系列分页信息"""
        self.series_current_page = pagination['current_page']
        self.series_total_pages = pagination['total_pages']

        self.series_page_label.setText(f"第 {self.series_current_page} 页，共 {self.series_total_pages} 页")

        self.series_prev_btn.setEnabled(pagination['has_prev'])
        self.series_next_btn.setEnabled(pagination['has_next'])

    def search_series(self):
        """搜索系列"""
        self.series_current_page = 1
        self.load_series()

    def filter_series(self):
        """筛选系列"""
        self.series_current_page = 1
        self.load_series()

    def refresh_series(self):
        """刷新系列列表"""
        self.load_series()

    def series_prev_page(self):
        """上一页"""
        if self.series_current_page > 1:
            self.series_current_page -= 1
            self.load_series()

    def series_next_page(self):
        """下一页"""
        if self.series_current_page < self.series_total_pages:
            self.series_current_page += 1
            self.load_series()

    def change_series_page_size(self, size_text):
        """改变每页大小"""
        self.series_page_size = int(size_text)
        self.series_current_page = 1
        self.load_series()

    def add_series(self):
        """新增系列"""
        dialog = SeriesEditDialog(self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            series_data = dialog.get_form_data()
            result = self.course_service.create_series(series_data)

            if result['success']:
                QMessageBox.information(self, '成功', result['message'])
                self.refresh_series()
            else:
                QMessageBox.warning(self, '失败', result['message'])

    def edit_series(self, series_data):
        """编辑系列"""
        dialog = SeriesEditDialog(self, series_data)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            form_data = dialog.get_form_data()
            result = self.course_service.update_series(series_data['id'], form_data)

            if result['success']:
                QMessageBox.information(self, '成功', result['message'])
                self.refresh_series()
            else:
                QMessageBox.warning(self, '失败', result['message'])

    def view_series(self, series_id):
        """查看系列详情"""
        result = self.course_service.get_series_detail(series_id)

        if result['success']:
            series_data = result['data']
            self.show_series_detail(series_data)
        else:
            QMessageBox.warning(self, '错误', result['message'])

    def show_series_detail(self, series_data):
        """显示系列详情"""
        dialog = QDialog(self)
        dialog.setWindowTitle(f"系列详情 - {series_data['title']}")
        dialog.setFixedSize(700, 600)

        layout = QVBoxLayout()

        # 基本信息
        info_group = QGroupBox("基本信息")
        info_layout = QFormLayout()

        info_layout.addRow('系列ID:', QLabel(str(series_data['id'])))
        info_layout.addRow('系列标题:', QLabel(series_data['title']))
        info_layout.addRow('系列描述:', QLabel(series_data['description']))
        info_layout.addRow('系列价格:', QLabel(f"¥{series_data['price']:.2f}"))
        info_layout.addRow('发布状态:', QLabel('已发布' if series_data['is_published'] else '未发布'))
        info_layout.addRow('视频数量:', QLabel(str(series_data.get('video_count', 0))))
        info_layout.addRow('总时长:', QLabel(series_data['total_duration_formatted']))
        info_layout.addRow('创建时间:', QLabel(series_data['created_at'][:19] if series_data['created_at'] else ''))

        info_group.setLayout(info_layout)
        layout.addWidget(info_group)

        # 视频列表
        video_group = QGroupBox("视频列表")
        video_layout = QVBoxLayout()

        video_text = QTextEdit()
        video_text.setReadOnly(True)

        video_info = ""
        for video in series_data['videos']:
            video_info += f"视频ID: {video['id']}\n"
            video_info += f"标题: {video['title']}\n"
            video_info += f"时长: {video['duration_formatted']}\n"
            video_info += f"排序: {video.get('order_index', 0)}\n"  # 使用get方法，避免KeyError
            video_info += f"创建时间: {video['created_at'][:19]}\n"
            video_info += "-" * 30 + "\n"

        if not video_info:
            video_info = "暂无视频"

        video_text.setText(video_info)
        video_layout.addWidget(video_text)
        video_group.setLayout(video_layout)
        layout.addWidget(video_group)

        # 关闭按钮
        close_btn = QPushButton('关闭')
        close_btn.clicked.connect(dialog.close)
        layout.addWidget(close_btn)

        dialog.setLayout(layout)
        dialog.exec()

    def toggle_series_publish(self, series_id, series_title):
        """切换系列发布状态"""
        result = self.course_service.toggle_series_publish(series_id)

        if result['success']:
            QMessageBox.information(self, '成功', result['message'])
            self.refresh_series()
        else:
            QMessageBox.warning(self, '失败', result['message'])

    def delete_series(self, series_id, series_title):
        """删除系列"""
        reply = QMessageBox.question(
            self,
            '确认删除',
            f'确定要删除系列 "{series_title}" 吗？\n\n注意：删除后无法恢复，且必须先删除关联的视频。',
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            result = self.course_service.delete_series(series_id)

            if result['success']:
                QMessageBox.information(self, '成功', result['message'])
                self.refresh_series()
            else:
                QMessageBox.warning(self, '失败', result['message'])

    def export_series(self):
        """导出系列数据"""
        try:
            # 获取所有系列数据
            result = self.course_service.get_series_list(page=1, page_size=100)

            if not result['success']:
                QMessageBox.warning(self, '导出失败', result['message'])
                return

            series_list = result['data']
            if not series_list:
                QMessageBox.information(self, '提示', '没有数据可导出')
                return

            # 导出到Excel
            exporter = ExcelExporter()
            filepath = exporter.export_series(series_list)

            # 提示成功并询问是否打开文件
            reply = QMessageBox.question(
                self,
                '导出成功',
                f'系列数据已导出到:\n{filepath}\n\n是否打开文件所在目录？',
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )

            if reply == QMessageBox.StandardButton.Yes:
                import os
                import subprocess
                import platform

                # 打开文件所在目录
                if platform.system() == "Windows":
                    os.startfile(os.path.dirname(filepath))
                elif platform.system() == "Darwin":  # macOS
                    subprocess.run(["open", os.path.dirname(filepath)])
                else:  # Linux
                    subprocess.run(["xdg-open", os.path.dirname(filepath)])

        except Exception as e:
            QMessageBox.critical(self, '导出失败', f'导出系列数据失败: {str(e)}')

    # ==================== 视频管理方法 ====================

    def load_videos(self):
        """加载视频数据"""
        if self.load_thread and self.load_thread.isRunning():
            return

        self.video_progress_bar.setVisible(True)
        self.video_progress_bar.setRange(0, 0)

        # 获取筛选条件
        search = self.video_search_edit.text().strip() or None

        # 暂时简化，直接调用服务
        try:
            result = self.course_service.get_video_list(
                page=self.video_current_page,
                page_size=self.video_page_size,
                search=search,
                series_id=None
            )
            self.on_videos_loaded(result)
        except Exception as e:
            self.on_load_error(str(e))
            return


    def on_videos_loaded(self, result):
        """视频数据加载完成"""
        self.video_progress_bar.setVisible(False)

        if result['success']:
            self.populate_video_table(result['data'])
            self.update_video_pagination(result['pagination'])
        else:
            QMessageBox.warning(self, '错误', result['message'])

    def populate_video_table(self, video_list):
        """填充视频表格数据"""
        self.video_table.setRowCount(len(video_list))

        for row, video in enumerate(video_list):
            # ID
            self.video_table.setItem(row, 0, QTableWidgetItem(str(video['id'])))

            # 视频标题
            self.video_table.setItem(row, 1, QTableWidgetItem(video['title']))

            # 所属分类
            category_title = video.get('category_title', '无关联分类')
            category_item = QTableWidgetItem(category_title)
            category_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.video_table.setItem(row, 2, category_item)

            # 所属系列
            series_title = video.get('series_title', '无关联系列')
            series_item = QTableWidgetItem(series_title)
            series_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.video_table.setItem(row, 3, series_item)

            # 分类价格
            category_price = video.get('category_price', 0)
            price_item = QTableWidgetItem(f"¥{category_price:.2f}")
            price_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.video_table.setItem(row, 4, price_item)

            # 时长
            duration_item = QTableWidgetItem(video.get('duration_formatted', '00:00'))
            duration_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.video_table.setItem(row, 5, duration_item)

            # 排序
            order_index = video.get('order_index', 0)  # 使用get方法，避免KeyError
            order_item = QTableWidgetItem(str(order_index))
            order_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.video_table.setItem(row, 6, order_item)

            # 创建时间
            created_at = video['created_at'][:19] if video['created_at'] else ''
            self.video_table.setItem(row, 7, QTableWidgetItem(created_at))

            # 操作按钮
            self.create_video_action_buttons(row, video)

    def create_video_action_buttons(self, row, video):
        """创建视频操作按钮"""
        widget = QWidget()
        layout = QHBoxLayout()
        layout.setContentsMargins(5, 2, 5, 2)

        # 编辑按钮
        edit_btn = QPushButton('编辑')
        edit_btn.setFixedSize(50, 25)
        edit_btn.clicked.connect(lambda: self.edit_video(video))

        # 删除按钮
        delete_btn = QPushButton('删除')
        delete_btn.setFixedSize(50, 25)
        delete_btn.clicked.connect(lambda: self.delete_video(video['id'], video['title']))

        layout.addWidget(edit_btn)
        layout.addWidget(delete_btn)

        widget.setLayout(layout)
        self.video_table.setCellWidget(row, 8, widget)

    def update_video_pagination(self, pagination):
        """更新视频分页信息"""
        self.video_current_page = pagination['current_page']
        self.video_total_pages = pagination['total_pages']

        self.video_page_label.setText(f"第 {self.video_current_page} 页，共 {self.video_total_pages} 页")

        self.video_prev_btn.setEnabled(pagination['has_prev'])
        self.video_next_btn.setEnabled(pagination['has_next'])

    def search_videos(self):
        """搜索视频"""
        self.video_current_page = 1
        self.load_videos()

    def filter_videos(self):
        """筛选视频"""
        self.video_current_page = 1
        self.load_videos()

    def refresh_videos(self):
        """刷新视频列表"""
        self.load_videos()

    def video_prev_page(self):
        """上一页"""
        if self.video_current_page > 1:
            self.video_current_page -= 1
            self.load_videos()

    def video_next_page(self):
        """下一页"""
        if self.video_current_page < self.video_total_pages:
            self.video_current_page += 1
            self.load_videos()

    def change_video_page_size(self, size_text):
        """改变每页大小"""
        self.video_page_size = int(size_text)
        self.video_current_page = 1
        self.load_videos()

    def add_video(self):
        """新增视频"""
        # 获取系列列表
        series_result = self.course_service.get_series_list(page=1, page_size=100)
        series_list = series_result['data'] if series_result['success'] else []

        dialog = VideoEditDialog(self, None, series_list)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            video_data = dialog.get_form_data()
            result = self.course_service.create_video(video_data)

            if result['success']:
                QMessageBox.information(self, '成功', result['message'])
                self.refresh_videos()
            else:
                QMessageBox.warning(self, '失败', result['message'])

    def batch_import_videos(self):
        """批量导入视频"""
        try:
            from ui.batch_import_dialog import BatchImportDialog

            dialog = BatchImportDialog(self, self.course_service)
            if dialog.exec() == QDialog.DialogCode.Accepted:
                # 导入成功，刷新视频列表
                self.refresh_videos()
                QMessageBox.information(self, '成功', '批量导入完成！')

        except Exception as e:
            QMessageBox.critical(self, '错误', f'打开批量导入对话框失败: {str(e)}')

    def edit_video(self, video_data):
        """编辑视频"""
        # 获取系列列表
        series_result = self.course_service.get_series_list(page=1, page_size=100)
        series_list = series_result['data'] if series_result['success'] else []

        dialog = VideoEditDialog(self, video_data, series_list)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            form_data = dialog.get_form_data()
            result = self.course_service.update_video(video_data['id'], form_data)

            if result['success']:
                QMessageBox.information(self, '成功', result['message'])
                self.refresh_videos()
            else:
                QMessageBox.warning(self, '失败', result['message'])

    def delete_video(self, video_id, video_title):
        """删除视频"""
        reply = QMessageBox.question(
            self,
            '确认删除',
            f'确定要删除视频 "{video_title}" 吗？\n\n注意：删除后无法恢复。',
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            result = self.course_service.delete_video(video_id)

            if result['success']:
                QMessageBox.information(self, '成功', result['message'])
                self.refresh_videos()
            else:
                QMessageBox.warning(self, '失败', result['message'])

    def export_videos(self):
        """导出视频数据"""
        try:
            # 获取所有视频数据
            result = self.course_service.get_video_list(page=1, page_size=100)

            if not result['success']:
                QMessageBox.warning(self, '导出失败', result['message'])
                return

            video_list = result['data']
            if not video_list:
                QMessageBox.information(self, '提示', '没有数据可导出')
                return

            # 导出到Excel
            exporter = ExcelExporter()
            # 转换数据格式以适应导出
            export_data = []
            for video in video_list:
                export_data.append({
                    'id': video['id'],
                    'title': video['title'],
                    'series_title': video['series_title'] or '无关联系列',
                    'duration_formatted': video['duration_formatted'],
                    'order_index': video.get('order_index', 0),  # 使用get方法，避免KeyError
                    'created_at': video['created_at']
                })

            # 使用CSV导出器
            from utils.exporters import CSVExporter
            csv_exporter = CSVExporter()
            filepath = csv_exporter.export_to_csv(
                export_data,
                'videos_export.csv',
                ['id', 'title', 'series_title', 'duration_formatted', 'order_index', 'created_at']
            )

            # 提示成功并询问是否打开文件
            reply = QMessageBox.question(
                self,
                '导出成功',
                f'视频数据已导出到:\n{filepath}\n\n是否打开文件所在目录？',
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )

            if reply == QMessageBox.StandardButton.Yes:
                import os
                import subprocess
                import platform

                # 打开文件所在目录
                if platform.system() == "Windows":
                    os.startfile(os.path.dirname(filepath))
                elif platform.system() == "Darwin":  # macOS
                    subprocess.run(["open", os.path.dirname(filepath)])
                else:  # Linux
                    subprocess.run(["xdg-open", os.path.dirname(filepath)])

        except Exception as e:
            QMessageBox.critical(self, '导出失败', f'导出视频数据失败: {str(e)}')

    # ==================== 通用方法 ====================

    def on_tab_changed_old(self, index):
        """旧的标签页切换事件（已废弃，保留备份）"""
        # 这个方法已被上面的缓存优化版本替代
        pass

    def update_video_series_filter(self):
        """更新视频筛选的系列列表"""
        # 清空现有选项
        self.video_series_combo.clear()
        self.video_series_combo.addItem('全部系列', None)

        # 获取系列列表
        result = self.course_service.get_series_list(page=1, page_size=100)
        if result['success']:
            for series in result['data']:
                self.video_series_combo.addItem(series['title'], series['id'])

    def on_load_error(self, error_msg):
        """加载错误处理"""
        self.series_progress_bar.setVisible(False)
        self.video_progress_bar.setVisible(False)
        self.category_progress_bar.setVisible(False)
        QMessageBox.critical(self, '加载失败', f'加载数据失败: {error_msg}')

    # ==================== 分类管理方法 ====================

    def load_categories(self):
        """加载分类列表"""
        # 首次加载时，先加载系列筛选选项（避免递归调用）
        if not hasattr(self, '_series_loaded_for_category'):
            self._series_loaded_for_category = True  # 先设置标志，避免递归
            self.load_series_for_category_filter()

        self.category_progress_bar.setVisible(True)

        # 获取筛选条件
        search = self.category_search_edit.text().strip() or None
        series_id = self.category_series_combo.currentData()

        # 暂时简化，直接调用服务
        try:
            print(f"🔍 加载分类列表: page={self.category_current_page}, page_size={self.category_page_size}, search={search}, series_id={series_id}")
            print(f"🔍 下拉框当前状态: 文本='{self.category_series_combo.currentText()}', 数据={self.category_series_combo.currentData()}, 索引={self.category_series_combo.currentIndex()}")
            result = self.course_service.get_category_list(
                page=self.category_current_page,
                page_size=self.category_page_size,
                search=search,
                series_id=series_id
            )
            print(f"📊 分类API返回: success={result.get('success')}, data_count={len(result.get('data', []))}")
            self.on_categories_loaded(result)
        except Exception as e:
            print(f"❌ 分类加载异常: {e}")
            self.on_load_error(str(e))
            return

    def on_categories_loaded(self, result):
        """分类加载完成"""
        self.category_progress_bar.setVisible(False)

        if result['success']:
            categories = result['data']
            pagination = result['pagination']

            # 更新分页信息
            self.category_total_pages = pagination['total_pages']
            self.category_page_label.setText(f"第 {pagination['current_page']} 页，共 {pagination['total_pages']} 页")

            # 更新按钮状态
            self.category_prev_btn.setEnabled(pagination['has_prev'])
            self.category_next_btn.setEnabled(pagination['has_next'])

            # 填充表格
            self.populate_category_table(categories)
        else:
            QMessageBox.critical(self, '加载失败', f"加载分类列表失败: {result['message']}")

    def populate_category_table(self, categories):
        """填充分类表格"""
        self.category_table.setRowCount(len(categories))

        for row, category in enumerate(categories):
            # ID
            self.category_table.setItem(row, 0, QTableWidgetItem(str(category['id'])))

            # 分类标题
            title_item = QTableWidgetItem(category['title'])
            title_item.setToolTip(category.get('description', ''))  # 鼠标悬停显示描述
            self.category_table.setItem(row, 1, title_item)

            # 所属系列
            series_item = QTableWidgetItem(category.get('series_title', ''))
            series_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.category_table.setItem(row, 2, series_item)

            # 分类价格
            price_item = QTableWidgetItem(f"¥{category['price']:.2f}")
            price_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.category_table.setItem(row, 3, price_item)

            # 视频数量
            video_count = category.get('video_count', 0)  # 默认为0，避免KeyError
            video_count_item = QTableWidgetItem(str(video_count))
            video_count_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.category_table.setItem(row, 4, video_count_item)

            # 总时长
            total_duration = category.get('total_duration', 0)
            duration_formatted = category.get('total_duration_formatted', self.format_duration(total_duration))
            duration_item = QTableWidgetItem(duration_formatted)
            duration_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.category_table.setItem(row, 5, duration_item)

            # 排序
            order_index = category.get('order_index', 0)  # 使用get方法，避免KeyError
            order_item = QTableWidgetItem(str(order_index))
            order_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.category_table.setItem(row, 6, order_item)

            # 创建时间
            created_at = category.get('created_at', '')
            if created_at:
                created_at = created_at[:19]  # 只显示到秒
            self.category_table.setItem(row, 7, QTableWidgetItem(created_at))

            # 操作按钮
            action_widget = QWidget()
            action_layout = QHBoxLayout(action_widget)
            action_layout.setContentsMargins(5, 2, 5, 2)

            edit_btn = QPushButton('编辑')
            edit_btn.setMaximumWidth(60)
            edit_btn.clicked.connect(lambda _, cat_id=category['id']: self.edit_category(cat_id))

            delete_btn = QPushButton('删除')
            delete_btn.setMaximumWidth(60)
            delete_btn.clicked.connect(lambda _, cat_id=category['id'], cat_title=category['title']: self.delete_category(cat_id, cat_title))

            action_layout.addWidget(edit_btn)
            action_layout.addWidget(delete_btn)
            action_layout.addStretch()

            self.category_table.setCellWidget(row, 8, action_widget)

    def format_duration(self, seconds):
        """格式化时长显示"""
        if not seconds:
            return "00:00"

        hours = seconds // 3600
        minutes = (seconds % 3600) // 60
        seconds = seconds % 60

        if hours > 0:
            return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
        else:
            return f"{minutes:02d}:{seconds:02d}"

    def search_categories(self):
        """搜索分类"""
        self.category_current_page = 1
        self.load_categories()

    def filter_categories(self):
        """筛选分类"""
        self.category_current_page = 1
        self.load_categories()

    def refresh_categories(self):
        """刷新分类列表"""
        self.load_categories()
        # 同时刷新系列下拉框
        self.load_series_for_category_filter()

    def add_category(self):
        """添加分类"""
        dialog = CategoryFormDialog(self.course_service, parent=self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            category_data = dialog.get_form_data()
            result = self.course_service.create_category(category_data)

            if result['success']:
                QMessageBox.information(self, '成功', '分类创建成功')
                self.refresh_categories()
            else:
                QMessageBox.critical(self, '失败', f"创建分类失败: {result['message']}")

    def edit_category(self, category_id):
        """编辑分类"""
        dialog = CategoryFormDialog(self.course_service, category_id, parent=self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            category_data = dialog.get_form_data()
            result = self.course_service.update_category(category_id, category_data)

            if result['success']:
                QMessageBox.information(self, '成功', '分类更新成功')
                self.refresh_categories()
            else:
                QMessageBox.critical(self, '失败', f"更新分类失败: {result['message']}")

    def delete_category(self, category_id, category_title):
        """删除分类"""
        reply = QMessageBox.question(
            self, '确认删除',
            f'确定要删除分类 "{category_title}" 吗？\n\n注意：删除后不可恢复！',
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            result = self.course_service.delete_category(category_id)

            if result['success']:
                QMessageBox.information(self, '成功', '分类删除成功')
                self.refresh_categories()
            else:
                QMessageBox.critical(self, '失败', f"删除分类失败: {result['message']}")

    def prev_category_page(self):
        """上一页"""
        if self.category_current_page > 1:
            self.category_current_page -= 1
            self.load_categories()

    def next_category_page(self):
        """下一页"""
        if self.category_current_page < self.category_total_pages:
            self.category_current_page += 1
            self.load_categories()

    def change_category_page_size(self):
        """改变每页显示数量"""
        self.category_page_size = int(self.category_page_size_combo.currentText())
        self.category_current_page = 1
        self.load_categories()

    def load_series_for_category_filter(self):
        """为分类筛选加载系列列表 - 使用缓存数据，避免重复API调用"""
        try:
            # 防止重复调用
            if hasattr(self, '_category_series_combo_loaded'):
                return
            self._category_series_combo_loaded = True

            # 使用已经预加载的系列数据，避免重复API调用
            print("📚 使用预加载的系列数据填充分类筛选下拉框...")
            self.category_series_combo.clear()
            self.category_series_combo.addItem('全部系列', None)

            # 如果有缓存的系列数据，直接使用
            if hasattr(self, '_cached_series_data') and self._cached_series_data:
                for series in self._cached_series_data:
                    self.category_series_combo.addItem(series['title'], series['id'])

                # 确保默认选中"全部系列"
                self.category_series_combo.setCurrentIndex(0)
                print(f"🔍 下拉框默认选中: {self.category_series_combo.currentText()}, 值: {self.category_series_combo.currentData()}")
                print(f"✅ 系列筛选下拉框填充完成: {len(self._cached_series_data)} 个系列")
            else:
                # 如果没有缓存，暂时跳过，等系列数据加载完成后再填充
                print("⚠️ 系列数据尚未加载，稍后自动填充...")
        except Exception as e:
            print(f"❌ 加载系列列表失败: {e}")

    def fill_category_series_combo(self):
        """填充分类筛选下拉框"""
        try:
            if hasattr(self, '_cached_series_data') and self._cached_series_data:
                print("📚 填充分类筛选下拉框...")
                self.category_series_combo.clear()
                self.category_series_combo.addItem('全部系列', None)

                for series in self._cached_series_data:
                    self.category_series_combo.addItem(series['title'], series['id'])

                # 确保默认选中"全部系列"
                self.category_series_combo.setCurrentIndex(0)
                print(f"🔍 下拉框默认选中: {self.category_series_combo.currentText()}, 值: {self.category_series_combo.currentData()}")

                self._category_series_combo_loaded = True
                print(f"✅ 分类筛选下拉框填充完成: {len(self._cached_series_data)} 个系列")
        except Exception as e:
            print(f"❌ 填充分类筛选下拉框失败: {e}")
