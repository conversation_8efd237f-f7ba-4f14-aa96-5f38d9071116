"""
API客户端
用于与服务端进行数据同步
"""

import requests
import json
import logging
from typing import Dict, Any, Optional, List
from urllib.parse import urljoin

logger = logging.getLogger(__name__)

class APIClient:
    """API客户端类"""
    
    def __init__(self, base_url: str = "http://localhost:8000", timeout: int = 30):
        self.base_url = base_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
        
        # 设置默认请求头
        self.session.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'X-User-Id': 'admin_001',  # 管理员用户ID
            'X-Is-Admin': 'true'       # 管理员权限标识
        })
    
    def _make_request(self, method: str, endpoint: str, **kwargs) -> Dict[str, Any]:
        """发送HTTP请求"""
        # 修复URL构造逻辑
        if endpoint.startswith('/'):
            url = self.base_url + endpoint
        else:
            url = f"{self.base_url}/{endpoint}"

        # 调试日志
        logger.info(f"API请求: {method} {url}")

        try:
            response = self.session.request(
                method=method,
                url=url,
                timeout=self.timeout,
                **kwargs
            )
            
            # 检查响应状态
            if response.status_code >= 400:
                error_msg = f"HTTP {response.status_code}: {response.text}"
                logger.error(f"API请求失败: {method} {url} -> {error_msg}")
                return {
                    'success': False,
                    'message': error_msg,
                    'data': None
                }
            
            # 解析JSON响应
            try:
                data = response.json()
                return data
            except json.JSONDecodeError:
                return {
                    'success': True,
                    'message': 'Success',
                    'data': response.text
                }
                
        except requests.exceptions.Timeout:
            error_msg = "请求超时"
            logger.error(f"API请求超时: {url}")
            return {
                'success': False,
                'message': error_msg,
                'data': None
            }
        except requests.exceptions.ConnectionError:
            error_msg = "连接服务端失败，请检查服务端是否启动"
            logger.error(f"连接失败: {url}")
            return {
                'success': False,
                'message': error_msg,
                'data': None
            }
        except Exception as e:
            error_msg = f"请求失败: {str(e)}"
            logger.error(f"API请求异常: {error_msg}")
            return {
                'success': False,
                'message': error_msg,
                'data': None
            }
    
    def get(self, endpoint: str, params: Optional[Dict] = None) -> Dict[str, Any]:
        """GET请求"""
        return self._make_request('GET', endpoint, params=params)
    
    def post(self, endpoint: str, data: Optional[Dict] = None) -> Dict[str, Any]:
        """POST请求"""
        return self._make_request('POST', endpoint, json=data)
    
    def put(self, endpoint: str, data: Optional[Dict] = None) -> Dict[str, Any]:
        """PUT请求"""
        return self._make_request('PUT', endpoint, json=data)
    
    def delete(self, endpoint: str) -> Dict[str, Any]:
        """DELETE请求"""
        return self._make_request('DELETE', endpoint)
    
    def test_connection(self) -> bool:
        """测试连接"""
        try:
            response = self.get('/')
            return response.get('success', True)
        except:
            return False

class CategoryAPIClient:
    """分类API客户端"""
    
    def __init__(self, api_client: APIClient):
        self.client = api_client
    
    def get_categories(self, page: int = 1, page_size: int = 20,
                      search: Optional[str] = None, series_id: Optional[str] = None) -> Dict[str, Any]:
        """获取分类列表"""
        try:
            print(f"🔍 CategoryAPIClient 接收到的参数: page={page}, page_size={page_size}, search={repr(search)}, series_id={repr(series_id)}")

            # 使用管理端专用的categories API
            params = {
                'page': page,
                'page_size': page_size
            }
            if search:
                params['search'] = search
            if series_id:
                params['series_id'] = series_id

            # 统一使用 /api/series 获取所有数据，然后提取分类
            response = self.client.get('/api/series')

            print(f"🔍 统一从 /api/series 获取数据: 类型={type(response)}, 长度={len(response) if isinstance(response, list) else 'N/A'}")

            # 从系列数据中提取分类
            if isinstance(response, list):
                categories = []
                for series in response:
                    series_title = series.get('title', '')
                    current_series_id = series.get('id', '')  # 使用不同的变量名，避免覆盖函数参数
                    for category in series.get('categories', []):
                        category_data = category.copy()
                        # 添加系列信息
                        category_data['series_title'] = series_title
                        category_data['series_id'] = current_series_id
                        # 计算视频数量
                        category_data['video_count'] = len(category.get('videos', []))
                        # 确保必要字段存在
                        category_data['created_at'] = category_data.get('created_at', '')
                        category_data['updated_at'] = category_data.get('updated_at', '')
                        categories.append(category_data)

                print(f"🔍 从系列数据提取的分类: 总数={len(categories)}")
                print(f"🔍 筛选参数: series_id={repr(series_id)}, search={repr(search)}")

                # 应用筛选 - 只有明确指定了series_id才筛选
                if series_id is not None and series_id != '' and series_id != 'None':
                    categories = [cat for cat in categories if cat.get('series_id') == series_id]
                    print(f"🔍 按系列筛选后: {len(categories)} 个分类")
                else:
                    print(f"🔍 跳过系列筛选，保留所有分类")

                if search:
                    categories = [cat for cat in categories if search.lower() in cat.get('title', '').lower()]
                    print(f"🔍 按搜索筛选后: {len(categories)} 个分类")

                print(f"🔍 最终返回分类数据: {len(categories)} 个")

                return {
                    'success': True,
                    'data': categories,
                    'pagination': {
                        'current_page': 1,
                        'page_size': len(categories),
                        'total_records': len(categories),
                        'total_pages': 1,
                        'has_next': False,
                        'has_prev': False
                    }
                }

            # 处理字典格式
            elif isinstance(response, dict):
                if 'pagination' in response:
                    return response

                if response.get('success') == False:
                    return {
                        'success': False,
                        'message': response.get('message', '获取分类列表失败'),
                        'data': [],
                        'pagination': {}
                    }

                data = response.get('data', response)
                if isinstance(data, list):
                    total = len(data)
                    start = (page - 1) * page_size
                    end = start + page_size
                    paginated_data = data[start:end]

                    return {
                        'success': True,
                        'data': paginated_data,
                        'pagination': {
                            'current_page': page,
                            'page_size': page_size,
                            'total_records': total,
                            'total_pages': (total + page_size - 1) // page_size,
                            'has_next': end < total,
                            'has_prev': page > 1
                        }
                    }
                else:
                    return {
                        'success': True,
                        'data': [data] if data else [],
                        'pagination': {
                            'current_page': 1,
                            'page_size': page_size,
                            'total_records': 1 if data else 0,
                            'total_pages': 1 if data else 0,
                            'has_next': False,
                            'has_prev': False
                        }
                    }

            else:
                return {
                    'success': False,
                    'message': f'获取分类列表失败: 未知响应格式 {type(response)}',
                    'data': [],
                    'pagination': {}
                }
        except Exception as e:
            return {
                'success': False,
                'message': f'获取分类列表失败: {str(e)}',
                'data': [],
                'pagination': {}
            }
    
    def get_category(self, category_id: str) -> Dict[str, Any]:
        """获取分类详情"""
        return self.client.get(f'/api/categories/{category_id}')
    
    def create_category(self, category_data: Dict[str, Any]) -> Dict[str, Any]:
        """创建分类"""
        # 转换字段名
        api_data = {
            'title': category_data.get('title'),
            'seriesId': str(category_data.get('series_id')),
            'price': float(category_data.get('price', 0)),
            'description': category_data.get('description'),
            'order_index': int(category_data.get('order_index', 0)),
            'isFree': category_data.get('price', 0) == 0
        }
        
        return self.client.post('/api/categories', data=api_data)
    
    def update_category(self, category_id: str, category_data: Dict[str, Any]) -> Dict[str, Any]:
        """更新分类到服务器"""
        try:
            print(f"🔄 正在更新分类 {category_id} 到服务器...")

            # 转换字段名
            api_data = {}

            if 'title' in category_data:
                api_data['title'] = category_data['title']
            if 'series_id' in category_data:
                api_data['seriesId'] = str(category_data['series_id'])
            if 'price' in category_data:
                api_data['price'] = float(category_data['price'])
                api_data['isFree'] = category_data['price'] == 0
            if 'description' in category_data:
                api_data['description'] = category_data['description']
            if 'order_index' in category_data:
                api_data['order_index'] = int(category_data['order_index'])

            response = self.client.put(f'/api/admin/categories/{category_id}', data=api_data)
            print(f"✅ 分类 {category_id} 服务器更新成功")
            return {'success': True, 'data': response}
        except Exception as e:
            print(f"❌ 分类 {category_id} 服务器更新失败: {e}")
            return {'success': False, 'message': str(e)}

    def get_all_categories(self) -> Dict[str, Any]:
        """获取所有分类（无分页）"""
        try:
            print("📂 获取所有分类数据（无分页）...")
            # 直接调用 /api/series 获取完整数据，然后提取分类
            response = self.client.get('/api/series')

            if isinstance(response, list):
                categories = []
                for series in response:
                    series_title = series.get('title', '')
                    current_series_id = series.get('id', '')
                    for category in series.get('categories', []):
                        category_data = category.copy()
                        category_data['series_title'] = series_title
                        category_data['series_id'] = current_series_id
                        category_data['video_count'] = len(category.get('videos', []))
                        category_data['created_at'] = category_data.get('created_at', '')
                        category_data['updated_at'] = category_data.get('updated_at', '')
                        categories.append(category_data)

                print(f"✅ 获取所有分类完成: {len(categories)} 个")
                return {'success': True, 'data': categories}
            else:
                return {'success': False, 'message': '数据格式错误', 'data': []}

        except Exception as e:
            print(f"❌ 获取所有分类失败: {e}")
            return {'success': False, 'message': str(e), 'data': []}

    def delete_category(self, category_id: str) -> Dict[str, Any]:
        """删除分类"""
        return self.client.delete(f'/api/categories/{category_id}')

class SeriesAPIClient:
    """系列API客户端"""
    
    def __init__(self, api_client: APIClient):
        self.client = api_client
    
    def get_series(self, page: int = 1, page_size: int = 20,
                  search: Optional[str] = None) -> Dict[str, Any]:
        """获取系列列表"""
        try:
            # 使用管理端专用的series API
            params = {
                'page': page,
                'page_size': page_size
            }
            if search:
                params['search'] = search

            response = self.client.get('/api/series', params=params)

            # 适配普通API返回格式，转换为管理端期望的格式
            logger.info(f"API响应类型: {type(response)}, 内容: {str(response)[:200]}")

            # 处理嵌套结构：提取系列数据并添加统计信息
            if isinstance(response, list):
                # 处理系列数据，添加统计信息
                series_data = []
                for series in response:
                    series_item = series.copy()
                    # 计算分类数量
                    categories = series.get('categories', [])
                    series_item['category_count'] = len(categories)
                    # 计算视频数量
                    video_count = sum(len(cat.get('videos', [])) for cat in categories)
                    series_item['video_count'] = video_count
                    # 确保必要字段存在
                    series_item['is_published'] = series_item.get('is_published', True)
                    series_item['total_duration_formatted'] = series_item.get('total_duration_formatted', '0分钟')
                    series_item['description'] = series_item.get('description', '暂无描述')
                    series_item['created_at'] = series_item.get('created_at', '')
                    series_item['updated_at'] = series_item.get('updated_at', '')
                    series_data.append(series_item)

                # 应用搜索筛选
                if search:
                    series_data = [s for s in series_data if search.lower() in s.get('title', '').lower()]

                # 不在API层做分页，返回所有数据
                print(f"🔍 最终返回系列数据: {len(series_data)} 个")

                return {
                    'success': True,
                    'data': series_data,  # 返回所有数据，不分页
                    'pagination': {
                        'current_page': 1,
                        'page_size': len(series_data),
                        'total_records': len(series_data),
                        'total_pages': 1,
                        'has_next': False,
                        'has_prev': False
                    }
                }

            # 处理字典格式
            elif isinstance(response, dict):
                # 检查是否已经是标准管理端格式
                if 'pagination' in response:
                    return response

                # 检查是否有success字段且为False
                if response.get('success') == False:
                    return {
                        'success': False,
                        'message': response.get('message', '获取系列列表失败'),
                        'data': [],
                        'pagination': {}
                    }

                # 提取数据部分
                data = response.get('data', response)
                if isinstance(data, list):
                    # 数据在data字段中
                    total = len(data)
                    start = (page - 1) * page_size
                    end = start + page_size
                    paginated_data = data[start:end]

                    return {
                        'success': True,
                        'data': paginated_data,
                        'pagination': {
                            'current_page': page,
                            'page_size': page_size,
                            'total_records': total,
                            'total_pages': (total + page_size - 1) // page_size,
                            'has_next': end < total,
                            'has_prev': page > 1
                        }
                    }
                else:
                    # 单个对象，包装成数组
                    return {
                        'success': True,
                        'data': [data] if data else [],
                        'pagination': {
                            'current_page': 1,
                            'page_size': page_size,
                            'total_records': 1 if data else 0,
                            'total_pages': 1 if data else 0,
                            'has_next': False,
                            'has_prev': False
                        }
                    }

            # 其他情况，返回失败
            else:
                return {
                    'success': False,
                    'message': f'获取系列列表失败: 未知响应格式 {type(response)}',
                    'data': [],
                    'pagination': {}
                }
        except Exception as e:
            return {
                'success': False,
                'message': f'获取系列列表失败: {str(e)}',
                'data': [],
                'pagination': {}
            }

    def update_series(self, series_id: str, series_data: Dict[str, Any]) -> Dict[str, Any]:
        """更新系列到服务器"""
        try:
            print(f"🔄 正在更新系列 {series_id} 到服务器...")
            response = self.client.put(f'/api/admin/series/{series_id}', data=series_data)
            print(f"✅ 系列 {series_id} 服务器更新成功")
            return {'success': True, 'data': response}
        except Exception as e:
            print(f"❌ 系列 {series_id} 服务器更新失败: {e}")
            return {'success': False, 'message': str(e)}

    def create_series(self, series_data: Dict[str, Any]) -> Dict[str, Any]:
        """创建系列到服务器"""
        try:
            print(f"🔄 正在创建系列到服务器...")
            response = self.client.post('/api/series', data=series_data)
            print(f"✅ 系列服务器创建成功")
            return {'success': True, 'data': response}
        except Exception as e:
            print(f"❌ 系列服务器创建失败: {e}")
            return {'success': False, 'message': str(e)}

    def delete_series(self, series_id: str) -> Dict[str, Any]:
        """删除系列从服务器"""
        try:
            print(f"🔄 正在从服务器删除系列 {series_id}...")
            response = self.client.delete(f'/api/series/{series_id}')
            print(f"✅ 系列 {series_id} 服务器删除成功")
            return {'success': True, 'data': response}
        except Exception as e:
            print(f"❌ 系列 {series_id} 服务器删除失败: {e}")
            return {'success': False, 'message': str(e)}

class VideoAPIClient:
    """视频API客户端"""
    
    def __init__(self, api_client: APIClient):
        self.client = api_client
    
    def get_videos(self, page: int = 1, page_size: int = 20,
                  search: Optional[str] = None, category_id: Optional[str] = None,
                  series_id: Optional[str] = None) -> Dict[str, Any]:
        """获取视频列表"""
        try:
            # 使用管理端专用的videos API
            params = {
                'page': page,
                'page_size': page_size
            }
            if search:
                params['search'] = search
            if category_id:
                params['category_id'] = category_id
            if series_id:
                params['series_id'] = series_id

            # 统一使用 /api/series 获取所有数据，然后提取视频
            response = self.client.get('/api/series')

            print(f"🔍 统一从 /api/series 获取视频数据: 类型={type(response)}, 长度={len(response) if isinstance(response, list) else 'N/A'}")

            # 从系列数据中提取视频
            if isinstance(response, list):
                videos = []
                for series in response:
                    series_title = series.get('title', '')
                    current_series_id = series.get('id', '')  # 使用不同的变量名，避免覆盖函数参数
                    for category in series.get('categories', []):
                        category_title = category.get('title', '')
                        current_category_id = category.get('id', '')  # 使用不同的变量名，避免覆盖函数参数
                        for video in category.get('videos', []):
                            # 添加系列和分类信息到视频
                            video_data = video.copy()
                            video_data['series_title'] = series_title
                            video_data['series_id'] = current_series_id
                            video_data['category_title'] = category_title
                            video_data['category_id'] = current_category_id
                            # 确保必要字段存在
                            video_data['created_at'] = video_data.get('created_at', '')
                            video_data['updated_at'] = video_data.get('updated_at', '')
                            videos.append(video_data)

                print(f"🔍 从系列数据提取的视频: 总数={len(videos)}")
                print(f"🔍 筛选参数: series_id={repr(series_id)}, category_id={repr(category_id)}, search={repr(search)}")

                # 应用筛选 - 只有明确指定了参数才筛选
                if series_id is not None and series_id != '' and series_id != 'None':
                    videos = [v for v in videos if v.get('series_id') == series_id]
                    print(f"🔍 按系列筛选后: {len(videos)} 个视频")
                else:
                    print(f"🔍 跳过系列筛选，保留所有视频")

                if category_id is not None and category_id != '' and category_id != 'None':
                    videos = [v for v in videos if v.get('category_id') == category_id]
                    print(f"🔍 按分类筛选后: {len(videos)} 个视频")
                else:
                    print(f"🔍 跳过分类筛选，保留所有视频")

                if search:
                    videos = [v for v in videos if search.lower() in v.get('title', '').lower()]
                    print(f"🔍 按搜索筛选后: {len(videos)} 个视频")

                print(f"🔍 最终返回视频数据: {len(videos)} 个")

                return {
                    'success': True,
                    'data': videos,
                    'pagination': {
                        'current_page': 1,
                        'page_size': len(videos),
                        'total_records': len(videos),
                        'total_pages': 1,
                        'has_next': False,
                        'has_prev': False
                    }
                }

            # 处理字典格式
            elif isinstance(response, dict):
                if 'pagination' in response:
                    return response

                if response.get('success') == False:
                    return {
                        'success': False,
                        'message': response.get('message', '获取视频列表失败'),
                        'data': [],
                        'pagination': {}
                    }

                data = response.get('data', response)
                if isinstance(data, list):
                    total = len(data)
                    start = (page - 1) * page_size
                    end = start + page_size
                    paginated_data = data[start:end]

                    return {
                        'success': True,
                        'data': paginated_data,
                        'pagination': {
                            'current_page': page,
                            'page_size': page_size,
                            'total_records': total,
                            'total_pages': (total + page_size - 1) // page_size,
                            'has_next': end < total,
                            'has_prev': page > 1
                        }
                    }
                else:
                    return {
                        'success': True,
                        'data': [data] if data else [],
                        'pagination': {
                            'current_page': 1,
                            'page_size': page_size,
                            'total_records': 1 if data else 0,
                            'total_pages': 1 if data else 0,
                            'has_next': False,
                            'has_prev': False
                        }
                    }

            else:
                return {
                    'success': False,
                    'message': f'获取视频列表失败: 未知响应格式 {type(response)}',
                    'data': [],
                    'pagination': {}
                }
        except Exception as e:
            return {
                'success': False,
                'message': f'获取视频列表失败: {str(e)}',
                'data': [],
                'pagination': {}
            }

    def update_video(self, video_id: str, video_data: Dict[str, Any]) -> Dict[str, Any]:
        """更新视频到服务器"""
        try:
            print(f"🔄 正在更新视频 {video_id} 到服务器...")
            response = self.client.put(f'/api/admin/videos/{video_id}', data=video_data)
            print(f"✅ 视频 {video_id} 服务器更新成功")
            return {'success': True, 'data': response}
        except Exception as e:
            print(f"❌ 视频 {video_id} 服务器更新失败: {e}")
            return {'success': False, 'message': str(e)}

    def create_video(self, video_data: Dict[str, Any]) -> Dict[str, Any]:
        """创建视频到服务器"""
        try:
            print(f"🔄 正在创建视频到服务器...")
            response = self.client.post('/api/videos', data=video_data)
            print(f"✅ 视频服务器创建成功")
            return {'success': True, 'data': response}
        except Exception as e:
            print(f"❌ 视频服务器创建失败: {e}")
            return {'success': False, 'message': str(e)}

    def delete_video(self, video_id: str) -> Dict[str, Any]:
        """删除视频从服务器"""
        try:
            print(f"🔄 正在从服务器删除视频 {video_id}...")
            response = self.client.delete(f'/api/videos/{video_id}')
            print(f"✅ 视频 {video_id} 服务器删除成功")
            return {'success': True, 'data': response}
        except Exception as e:
            print(f"❌ 视频 {video_id} 服务器删除失败: {e}")
            return {'success': False, 'message': str(e)}

    def get_all_videos(self) -> Dict[str, Any]:
        """获取所有视频（无分页）"""
        try:
            print("📹 获取所有视频数据（无分页）...")
            # 直接调用 /api/series 获取完整数据，然后提取视频
            response = self.client.get('/api/series')

            if isinstance(response, list):
                videos = []
                for series in response:
                    series_title = series.get('title', '')
                    current_series_id = series.get('id', '')
                    for category in series.get('categories', []):
                        category_title = category.get('title', '')
                        current_category_id = category.get('id', '')
                        for video in category.get('videos', []):
                            video_data = video.copy()
                            video_data['series_title'] = series_title
                            video_data['series_id'] = current_series_id
                            video_data['category_title'] = category_title
                            video_data['category_id'] = current_category_id
                            video_data['created_at'] = video_data.get('created_at', '')
                            video_data['updated_at'] = video_data.get('updated_at', '')
                            videos.append(video_data)

                print(f"✅ 获取所有视频完成: {len(videos)} 个")
                return {'success': True, 'data': videos}
            else:
                return {'success': False, 'message': '数据格式错误', 'data': []}

        except Exception as e:
            print(f"❌ 获取所有视频失败: {e}")
            return {'success': False, 'message': str(e), 'data': []}


# 全局API客户端实例
def create_api_clients():
    """创建API客户端实例"""
    try:
        from config.api_config import api_config
        base_url = api_config.api_base_url
        timeout = api_config.api_timeout
    except:
        base_url = "http://localhost:8000"
        timeout = 30

    client = APIClient(base_url, timeout)
    return client, CategoryAPIClient(client), SeriesAPIClient(client), VideoAPIClient(client), AdminUserAPIClient(client)

class AdminUserAPIClient:
    """管理端用户数据API客户端"""

    def __init__(self, client):
        self.client = client

    def update_user(self, user_id: str, user_data: Dict[str, Any]) -> Dict[str, Any]:
        """更新用户基本信息"""
        try:
            print(f"🔄 正在更新用户 {user_id} 基本信息...")
            response = self.client.put(f'/api/admin/users/{user_id}', data=user_data)
            print(f"✅ 用户 {user_id} 基本信息更新成功")
            return {'success': True, 'data': response}
        except Exception as e:
            print(f"❌ 用户 {user_id} 基本信息更新失败: {e}")
            return {'success': False, 'message': str(e)}

    def update_user_purchase(self, user_id: str, purchase_id: str, purchase_data: Dict[str, Any]) -> Dict[str, Any]:
        """更新用户购买记录"""
        try:
            print(f"🔄 正在更新用户 {user_id} 购买记录 {purchase_id}...")
            response = self.client.put(f'/api/admin/users/{user_id}/purchases/{purchase_id}', data=purchase_data)
            print(f"✅ 用户 {user_id} 购买记录更新成功")
            return {'success': True, 'data': response}
        except Exception as e:
            print(f"❌ 用户 {user_id} 购买记录更新失败: {e}")
            return {'success': False, 'message': str(e)}

    def update_user_progress(self, user_id: str, video_id: str, progress_data: Dict[str, Any]) -> Dict[str, Any]:
        """更新用户观看进度"""
        try:
            print(f"🔄 正在更新用户 {user_id} 视频 {video_id} 观看进度...")
            response = self.client.put(f'/api/admin/users/{user_id}/progress/{video_id}', data=progress_data)
            print(f"✅ 用户 {user_id} 观看进度更新成功")
            return {'success': True, 'data': response}
        except Exception as e:
            print(f"❌ 用户 {user_id} 观看进度更新失败: {e}")
            return {'success': False, 'message': str(e)}

    def update_user_cache(self, user_id: str, video_id: str, cache_data: Dict[str, Any]) -> Dict[str, Any]:
        """更新用户缓存状态"""
        try:
            print(f"🔄 正在更新用户 {user_id} 视频 {video_id} 缓存状态...")
            response = self.client.put(f'/api/admin/users/{user_id}/cache/{video_id}', data=cache_data)
            print(f"✅ 用户 {user_id} 缓存状态更新成功")
            return {'success': True, 'data': response}
        except Exception as e:
            print(f"❌ 用户 {user_id} 缓存状态更新失败: {e}")
            return {'success': False, 'message': str(e)}

    def update_user_favorite(self, user_id: str, item_id: str, favorite_data: Dict[str, Any]) -> Dict[str, Any]:
        """更新用户收藏"""
        try:
            print(f"🔄 正在更新用户 {user_id} 收藏项目 {item_id}...")
            response = self.client.put(f'/api/admin/users/{user_id}/favorites/{item_id}', data=favorite_data)
            print(f"✅ 用户 {user_id} 收藏更新成功")
            return {'success': True, 'data': response}
        except Exception as e:
            print(f"❌ 用户 {user_id} 收藏更新失败: {e}")
            return {'success': False, 'message': str(e)}

    def update_user_settings(self, user_id: str, settings_data: Dict[str, Any]) -> Dict[str, Any]:
        """更新用户设置"""
        try:
            print(f"🔄 正在更新用户 {user_id} 设置...")
            response = self.client.put(f'/api/admin/users/{user_id}/settings', data=settings_data)
            print(f"✅ 用户 {user_id} 设置更新成功")
            return {'success': True, 'data': response}
        except Exception as e:
            print(f"❌ 用户 {user_id} 设置更新失败: {e}")
            return {'success': False, 'message': str(e)}

api_client, category_api, series_api, video_api, admin_user_api = create_api_clients()
