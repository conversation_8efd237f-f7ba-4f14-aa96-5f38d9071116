#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据访问对象 (DAO)
"""

from typing import List, Optional, Dict, Any, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, asc, func
from database.models import User, Series, Category, Video, Order
import logging

logger = logging.getLogger(__name__)

class BaseDAO:
    """基础DAO类"""

    def __init__(self, session: Session):
        self.session = session
        self.model = None  # 子类需要设置对应的模型类

    def commit(self):
        """提交事务"""
        try:
            self.session.commit()
        except Exception as e:
            self.session.rollback()
            logger.error(f"数据库提交失败: {e}")
            raise

    def rollback(self):
        """回滚事务"""
        self.session.rollback()

    def get_by_id(self, id: int):
        """根据ID获取记录"""
        if not self.model:
            raise NotImplementedError("子类必须设置model属性")
        try:
            return self.session.query(self.model).filter(self.model.id == id).first()
        except Exception as e:
            logger.error(f"根据ID获取记录失败: {e}")
            return None

    def create(self, data: dict):
        """创建记录"""
        if not self.model:
            raise NotImplementedError("子类必须设置model属性")
        try:
            instance = self.model(**data)
            self.session.add(instance)
            self.session.commit()
            return instance
        except Exception as e:
            self.session.rollback()
            logger.error(f"创建记录失败: {e}")
            raise

    def update(self, id: int, data: dict):
        """更新记录"""
        if not self.model:
            raise NotImplementedError("子类必须设置model属性")
        try:
            instance = self.get_by_id(id)
            if instance:
                for key, value in data.items():
                    if hasattr(instance, key):
                        setattr(instance, key, value)
                self.session.commit()
                return instance
            return None
        except Exception as e:
            self.session.rollback()
            logger.error(f"更新记录失败: {e}")
            raise

    def delete(self, id: int):
        """删除记录"""
        if not self.model:
            raise NotImplementedError("子类必须设置model属性")
        try:
            instance = self.get_by_id(id)
            if instance:
                self.session.delete(instance)
                self.session.commit()
                return True
            return False
        except Exception as e:
            self.session.rollback()
            logger.error(f"删除记录失败: {e}")
            raise

class UserDAO(BaseDAO):
    """用户数据访问对象"""
    
    def get_all(self, page: int = 1, page_size: int = 20, 
                search: str = None, is_active: bool = None) -> Tuple[List[User], int]:
        """获取用户列表"""
        query = self.session.query(User)
        
        # 搜索条件
        if search:
            search_filter = or_(
                User.username.like(f'%{search}%'),
                User.email.like(f'%{search}%')
            )
            query = query.filter(search_filter)
        
        # 状态筛选
        if is_active is not None:
            query = query.filter(User.is_active == is_active)
        
        # 总数
        total = query.count()
        
        # 分页
        offset = (page - 1) * page_size
        users = query.order_by(desc(User.created_at)).offset(offset).limit(page_size).all()
        
        return users, total
    
    def get_by_id(self, user_id: int) -> Optional[User]:
        """根据ID获取用户"""
        return self.session.query(User).filter(User.id == user_id).first()
    
    def get_by_username(self, username: str) -> Optional[User]:
        """根据用户名获取用户"""
        return self.session.query(User).filter(User.username == username).first()
    
    def get_by_email(self, email: str) -> Optional[User]:
        """根据邮箱获取用户"""
        return self.session.query(User).filter(User.email == email).first()
    
    def create(self, user_data: Dict[str, Any]) -> User:
        """创建用户"""
        user = User(**user_data)
        self.session.add(user)
        self.commit()
        return user
    
    def update(self, user_id: int, user_data: Dict[str, Any]) -> Optional[User]:
        """更新用户"""
        user = self.get_by_id(user_id)
        if user:
            for key, value in user_data.items():
                if hasattr(user, key):
                    setattr(user, key, value)
            self.commit()
        return user
    
    def delete(self, user_id: int) -> bool:
        """删除用户（软删除）"""
        user = self.get_by_id(user_id)
        if user:
            user.is_active = False
            self.commit()
            return True
        return False
    
    def hard_delete(self, user_id: int) -> bool:
        """硬删除用户"""
        user = self.get_by_id(user_id)
        if user:
            self.session.delete(user)
            self.commit()
            return True
        return False
    
    def get_user_orders(self, user_id: int) -> List[Order]:
        """获取用户订单"""
        return self.session.query(Order).filter(Order.user_id == user_id).order_by(desc(Order.created_at)).all()
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取用户统计信息"""
        total_users = self.session.query(User).count()
        active_users = self.session.query(User).filter(User.is_active == True).count()
        inactive_users = total_users - active_users
        
        # 最近30天新增用户
        from datetime import datetime, timedelta
        thirty_days_ago = datetime.now() - timedelta(days=30)
        new_users_30d = self.session.query(User).filter(User.created_at >= thirty_days_ago).count()
        
        return {
            'total_users': total_users,
            'active_users': active_users,
            'inactive_users': inactive_users,
            'new_users_30d': new_users_30d
        }

class SeriesDAO(BaseDAO):
    """系列数据访问对象"""

    def __init__(self, session: Session):
        super().__init__(session)
        self.model = Series

    def get_all(self, page: int = 1, page_size: int = 20,
                search: str = None, is_published: bool = None) -> Tuple[List[Series], int]:
        """获取系列列表"""
        query = self.session.query(Series)
        
        # 搜索条件
        if search:
            search_filter = or_(
                Series.title.like(f'%{search}%'),
                Series.description.like(f'%{search}%')
            )
            query = query.filter(search_filter)
        
        # 发布状态筛选
        if is_published is not None:
            query = query.filter(Series.is_published == is_published)
        
        # 总数
        total = query.count()
        
        # 分页
        offset = (page - 1) * page_size
        series = query.order_by(desc(Series.created_at)).offset(offset).limit(page_size).all()
        
        return series, total
    
    def get_by_id(self, series_id: str) -> Optional[Series]:
        """根据ID获取系列（字符串ID）"""
        return self.session.query(Series).filter(Series.id == series_id).first()
    
    def create(self, series_data: Dict[str, Any]) -> Series:
        """创建系列"""
        import uuid
        import time

        # 如果没有提供ID，自动生成一个
        if 'id' not in series_data or not series_data['id']:
            # 生成基于时间戳和随机数的唯一ID
            timestamp = str(int(time.time()))
            random_suffix = str(uuid.uuid4())[:8]
            series_data['id'] = f"series-{timestamp}-{random_suffix}"

        series = Series(**series_data)
        self.session.add(series)
        self.commit()
        return series
    
    def update(self, series_id: str, series_data: Dict[str, Any]) -> Optional[Series]:
        """更新系列（字符串ID）"""
        series = self.get_by_id(series_id)
        if series:
            for key, value in series_data.items():
                if hasattr(series, key):
                    setattr(series, key, value)
            self.commit()
        return series
    
    def delete(self, series_id: int) -> bool:
        """删除系列"""
        series = self.get_by_id(series_id)
        if series:
            self.session.delete(series)
            self.commit()
            return True
        return False
    
    def get_series_videos(self, series_id: int) -> List[Video]:
        """获取系列视频"""
        return self.session.query(Video).filter(Video.series_id == series_id).order_by(Video.order_index).all()

class CategoryDAO(BaseDAO):
    """分类数据访问对象"""

    def __init__(self, session: Session):
        super().__init__(session)
        self.model = Category

    def create(self, category_data: Dict[str, Any]) -> Category:
        """创建分类"""
        import uuid
        import time

        # 如果没有提供ID，自动生成一个
        if 'id' not in category_data or not category_data['id']:
            # 生成基于时间戳和随机数的唯一ID
            timestamp = str(int(time.time()))
            random_suffix = str(uuid.uuid4())[:8]
            category_data['id'] = f"category-{timestamp}-{random_suffix}"

        category = Category(**category_data)
        self.session.add(category)
        self.commit()
        return category

    def get_by_id(self, category_id: str) -> Optional[Category]:
        """根据ID获取分类（字符串ID）"""
        try:
            return self.session.query(Category).filter(Category.id == category_id).first()
        except Exception as e:
            logger.error(f"根据ID获取分类失败: {e}")
            return None

    def get_all(self, page: int = 1, page_size: int = 20,
                search: str = None, series_id: str = None) -> Tuple[List[Category], int]:
        """获取分类列表"""
        try:
            query = self.session.query(Category)

            # 搜索条件
            if search:
                query = query.filter(
                    or_(
                        Category.title.like(f'%{search}%'),
                        Category.description.like(f'%{search}%')
                    )
                )

            # 系列筛选
            if series_id:
                query = query.filter(Category.series_id == series_id)

            # 排序
            query = query.order_by(Category.series_id, Category.order_index, Category.id)

            # 获取总数
            total = query.count()

            # 分页
            offset = (page - 1) * page_size
            categories = query.offset(offset).limit(page_size).all()

            return categories, total
        except Exception as e:
            logger.error(f"获取分类列表失败: {e}")
            return [], 0

    def get_by_series_id(self, series_id: int) -> List[Category]:
        """根据系列ID获取分类列表"""
        try:
            return self.session.query(Category).filter(
                Category.series_id == series_id
            ).order_by(Category.order_index, Category.id).all()
        except Exception as e:
            logger.error(f"获取系列分类失败: {e}")
            return []

    def update(self, category_id: str, category_data: Dict[str, Any]) -> Optional[Category]:
        """更新分类（字符串ID）"""
        try:
            category = self.get_by_id(category_id)
            if category:
                for key, value in category_data.items():
                    if hasattr(category, key):
                        setattr(category, key, value)
                self.commit()
            return category
        except Exception as e:
            self.session.rollback()
            logger.error(f"更新分类失败: {e}")
            raise

    def get_category_videos(self, category_id: str) -> List[Video]:
        """获取分类下的视频列表"""
        try:
            return self.session.query(Video).filter(
                Video.category_id == category_id
            ).order_by(Video.order_index, Video.id).all()
        except Exception as e:
            logger.error(f"获取分类视频失败: {e}")
            return []

class VideoDAO(BaseDAO):
    """视频数据访问对象"""

    def __init__(self, session: Session):
        super().__init__(session)
        self.model = Video

    def get_all(self, page: int = 1, page_size: int = 20,
                search: str = None, category_id: int = None, series_id: int = None) -> Tuple[List[Video], int]:
        """获取视频列表"""
        query = self.session.query(Video)

        # 搜索条件
        if search:
            search_filter = or_(
                Video.title.like(f'%{search}%'),
                Video.description.like(f'%{search}%')
            )
            query = query.filter(search_filter)

        # 分类筛选
        if category_id:
            query = query.filter(Video.category_id == category_id)

        # 系列筛选（通过分类关联）
        if series_id:
            query = query.join(Category).filter(Category.series_id == series_id)

        # 总数
        total = query.count()

        # 分页
        offset = (page - 1) * page_size
        videos = query.order_by(Video.category_id, Video.order_index).offset(offset).limit(page_size).all()

        return videos, total
    
    def get_by_id(self, video_id: int) -> Optional[Video]:
        """根据ID获取视频"""
        return self.session.query(Video).filter(Video.id == video_id).first()
    
    def create(self, video_data: Dict[str, Any]) -> Video:
        """创建视频"""
        video = Video(**video_data)
        self.session.add(video)
        self.commit()
        return video
    
    def update(self, video_id: int, video_data: Dict[str, Any]) -> Optional[Video]:
        """更新视频"""
        video = self.get_by_id(video_id)
        if video:
            for key, value in video_data.items():
                if hasattr(video, key):
                    setattr(video, key, value)
            self.commit()
        return video
    
    def delete(self, video_id: int) -> bool:
        """删除视频"""
        video = self.get_by_id(video_id)
        if video:
            self.session.delete(video)
            self.commit()
            return True
        return False

class OrderDAO(BaseDAO):
    """订单数据访问对象"""
    
    def get_all(self, page: int = 1, page_size: int = 20, 
                search: str = None, status: str = None) -> Tuple[List[Order], int]:
        """获取订单列表"""
        query = self.session.query(Order).join(User).join(Series)
        
        # 搜索条件
        if search:
            search_filter = or_(
                User.username.like(f'%{search}%'),
                User.email.like(f'%{search}%'),
                Series.title.like(f'%{search}%')
            )
            query = query.filter(search_filter)
        
        # 状态筛选
        if status:
            query = query.filter(Order.status == status)
        
        # 总数
        total = query.count()
        
        # 分页
        offset = (page - 1) * page_size
        orders = query.order_by(desc(Order.created_at)).offset(offset).limit(page_size).all()
        
        return orders, total
    
    def get_by_id(self, order_id: int) -> Optional[Order]:
        """根据ID获取订单"""
        return self.session.query(Order).filter(Order.id == order_id).first()
    
    def create(self, order_data: Dict[str, Any]) -> Order:
        """创建订单"""
        order = Order(**order_data)
        self.session.add(order)
        self.commit()
        return order
    
    def update(self, order_id: int, order_data: Dict[str, Any]) -> Optional[Order]:
        """更新订单"""
        order = self.get_by_id(order_id)
        if order:
            for key, value in order_data.items():
                if hasattr(order, key):
                    setattr(order, key, value)
            self.commit()
        return order
    
    def get_revenue_statistics(self) -> Dict[str, Any]:
        """获取收入统计"""
        # 总收入
        total_revenue = self.session.query(func.sum(Order.amount)).filter(Order.status == 'completed').scalar() or 0
        
        # 订单统计
        total_orders = self.session.query(Order).count()
        completed_orders = self.session.query(Order).filter(Order.status == 'completed').count()
        pending_orders = self.session.query(Order).filter(Order.status == 'pending').count()
        cancelled_orders = self.session.query(Order).filter(Order.status == 'cancelled').count()
        
        return {
            'total_revenue': float(total_revenue),
            'total_orders': total_orders,
            'completed_orders': completed_orders,
            'pending_orders': pending_orders,
            'cancelled_orders': cancelled_orders
        }
