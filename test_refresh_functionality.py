#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试刷新功能
验证管理端刷新按钮能否从服务器重新加载最新数据
"""

import sys
import os

# 添加src目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from cache.global_data_manager import global_data_manager

def test_force_reload():
    """测试强制重新加载功能"""
    print("🧪 测试强制重新加载功能...")
    
    # 1. 首次加载数据
    print("\n📦 步骤1: 首次加载数据...")
    success = global_data_manager.load_all_data_once()
    if not success:
        print("❌ 首次数据加载失败")
        return False
    
    # 获取首次加载的数据
    first_summary = global_data_manager.get_data_summary()
    print(f"首次加载数据统计: {first_summary}")
    
    # 获取第一个系列的标题
    series_result = global_data_manager.get_series_list(page=1, page_size=1)
    if series_result['success'] and series_result['data']:
        first_series_title = series_result['data'][0]['title']
        print(f"首次加载的第一个系列标题: {first_series_title}")
    else:
        print("❌ 获取系列数据失败")
        return False
    
    # 2. 测试重复加载（应该跳过）
    print("\n📦 步骤2: 测试重复加载（应该跳过）...")
    success = global_data_manager.load_all_data_once()
    if success:
        print("✅ 重复加载正确跳过")
    else:
        print("❌ 重复加载失败")
        return False
    
    # 3. 测试强制重新加载
    print("\n🔄 步骤3: 测试强制重新加载...")
    success = global_data_manager.force_reload_data()
    if not success:
        print("❌ 强制重新加载失败")
        return False
    
    # 获取重新加载后的数据
    second_summary = global_data_manager.get_data_summary()
    print(f"重新加载数据统计: {second_summary}")
    
    # 获取重新加载后的第一个系列标题
    series_result = global_data_manager.get_series_list(page=1, page_size=1)
    if series_result['success'] and series_result['data']:
        second_series_title = series_result['data'][0]['title']
        print(f"重新加载的第一个系列标题: {second_series_title}")
    else:
        print("❌ 重新获取系列数据失败")
        return False
    
    # 4. 验证数据是否更新
    print(f"\n📊 步骤4: 验证数据更新...")
    print(f"首次标题: {first_series_title}")
    print(f"重新加载标题: {second_series_title}")
    
    if first_series_title != second_series_title:
        print("🎉 检测到数据更新！刷新功能正常工作")
    else:
        print("ℹ️ 数据未发生变化，但刷新功能正常工作")
    
    return True

def test_clear_cache():
    """测试清除缓存功能"""
    print("\n🗑️ 测试清除缓存功能...")
    
    # 确保数据已加载
    global_data_manager.load_all_data_once()
    
    # 检查数据状态
    if global_data_manager.is_data_loaded():
        print("✅ 数据已加载")
    else:
        print("❌ 数据未加载")
        return False
    
    # 清除缓存
    global_data_manager.clear_cache()
    
    # 检查清除后的状态
    if not global_data_manager.is_data_loaded():
        print("✅ 缓存清除成功")
        return True
    else:
        print("❌ 缓存清除失败")
        return False

def test_data_consistency():
    """测试数据一致性"""
    print("\n🔍 测试数据一致性...")
    
    # 加载数据
    global_data_manager.force_reload_data()
    
    # 获取各类数据
    series_result = global_data_manager.get_series_list(page=1, page_size=100)
    category_result = global_data_manager.get_category_list(page=1, page_size=100)
    video_result = global_data_manager.get_video_list(page=1, page_size=100)
    
    if not all([series_result['success'], category_result['success'], video_result['success']]):
        print("❌ 数据获取失败")
        return False
    
    series_count = len(series_result['data'])
    category_count = len(category_result['data'])
    video_count = len(video_result['data'])
    
    print(f"📊 数据统计: 系列={series_count}, 分类={category_count}, 视频={video_count}")
    
    # 验证数据完整性
    if series_count > 0 and category_count > 0 and video_count > 0:
        print("✅ 数据完整性验证通过")
        return True
    else:
        print("❌ 数据完整性验证失败")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("管理端刷新功能测试")
    print("=" * 60)
    
    try:
        # 测试强制重新加载
        reload_success = test_force_reload()
        
        if reload_success:
            # 测试清除缓存
            cache_success = test_clear_cache()
            
            # 测试数据一致性
            consistency_success = test_data_consistency()
            
            if cache_success and consistency_success:
                print("\n" + "=" * 60)
                print("🎉 所有测试通过！刷新功能正常工作。")
                print("现在点击管理端的刷新按钮应该可以从服务器重新加载最新数据了。")
                print("=" * 60)
                
                print("\n💡 使用说明：")
                print("1. 修改服务端数据文件（如 series.json）")
                print("2. 在管理端点击任意标签页的'刷新'按钮")
                print("3. 系统会强制从服务器重新加载所有数据")
                print("4. 界面会显示最新的数据")
            else:
                print("\n⚠️ 部分测试失败")
        else:
            print("\n❌ 强制重新加载测试失败")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
