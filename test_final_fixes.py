#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终修复验证测试
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_series_table_initialization():
    """测试系列表格初始化"""
    print("🔍 测试系列表格初始化...")
    
    try:
        from PyQt6.QtWidgets import QApplication
        from ui.main_window import MainWindow
        from utils.config import Config
        import time
        
        # 创建应用
        app = QApplication(sys.argv)
        
        # 创建主窗口
        config = Config()
        main_window = MainWindow(config)
        main_window.show()
        
        # 显示课程页面
        main_window.show_course_page()
        time.sleep(3)  # 等待初始化完成
        
        # 获取课程窗口
        if hasattr(main_window, '_course_widget'):
            course_widget = main_window._course_widget
            
            # 切换到系列管理标签页
            print("📚 切换到系列管理标签页...")
            course_widget.tab_widget.setCurrentIndex(1)  # 系列管理是第1个标签页
            time.sleep(2)  # 等待标签页切换完成
            
            # 检查表格行数
            table_row_count = course_widget.series_table.rowCount()
            print(f"📋 系列表格行数: {table_row_count}")
            
            # 检查缓存数据
            cache_count = 0
            if hasattr(course_widget, '_cached_series_data'):
                cache_count = len(course_widget._cached_series_data)
                print(f"📦 本地缓存系列数量: {cache_count}")
            
            # 验证表格是否正确加载了数据
            if table_row_count > 0 and table_row_count == cache_count:
                print("✅ 系列表格初始化正常")
                return True
            else:
                print(f"❌ 系列表格初始化异常: 表格行数={table_row_count}, 缓存数量={cache_count}")
                return False
        else:
            print("❌ 课程窗口创建失败")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        try:
            app.quit()
        except:
            pass

def test_complete_data_format():
    """测试数据格式补全功能"""
    print("\n🔍 测试数据格式补全功能...")
    
    try:
        from PyQt6.QtWidgets import QApplication
        from ui.main_window import MainWindow
        from utils.config import Config
        
        # 创建应用
        app = QApplication(sys.argv)
        
        # 创建主窗口
        config = Config()
        main_window = MainWindow(config)
        main_window.show()
        
        # 显示课程页面
        main_window.show_course_page()
        
        # 获取课程窗口
        if hasattr(main_window, '_course_widget'):
            course_widget = main_window._course_widget
            
            # 测试数据格式补全
            test_data = {
                'id': 'test-series-123',
                'title': '测试系列',
                'description': '测试描述',
                'price': 99.99,
                'is_published': False
            }
            
            print("📝 原始数据:")
            for key, value in test_data.items():
                print(f"   {key}: {value}")
            
            # 调用数据格式补全方法
            complete_data = course_widget.complete_series_data_format(test_data)
            
            print("\n📋 补全后数据:")
            for key, value in complete_data.items():
                print(f"   {key}: {value}")
            
            # 验证必需字段是否存在
            required_fields = ['id', 'title', 'description', 'price', 'is_published', 
                             'category_count', 'video_count', 'created_at', 'updated_at']
            
            missing_fields = [field for field in required_fields if field not in complete_data]
            
            if not missing_fields:
                print("✅ 数据格式补全正常，所有必需字段都存在")
                return True
            else:
                print(f"❌ 数据格式补全异常，缺少字段: {missing_fields}")
                return False
        else:
            print("❌ 课程窗口创建失败")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        try:
            app.quit()
        except:
            pass

def main():
    """主测试函数"""
    print("=" * 60)
    print("最终修复验证测试")
    print("=" * 60)
    
    test_results = []
    
    # 测试系列表格初始化
    test_results.append(("系列表格初始化", test_series_table_initialization()))
    
    # 测试数据格式补全
    test_results.append(("数据格式补全", test_complete_data_format()))
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("最终测试结果")
    print("=" * 60)
    
    for test_name, result in test_results:
        status = "✅ 正常" if result else "❌ 异常"
        print(f"{test_name}: {status}")
    
    failed_tests = [name for name, result in test_results if not result]
    if failed_tests:
        print(f"\n⚠️ 需要进一步修复: {', '.join(failed_tests)}")
    else:
        print("\n🎉 所有问题都已修复！")
        print("\n✅ 修复成果：")
        print("- 📋 系列表格正确初始化，显示所有数据")
        print("- 📝 新创建的系列数据格式完整")
        print("- 💾 缓存和表格数据一致")
        print("- 🔄 标签页切换正常工作")
        print("\n🎊 现在管理端功能完全正常！")

if __name__ == "__main__":
    main()
