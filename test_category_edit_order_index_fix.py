#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分类编辑order_index字段问题修正验证测试
验证分类编辑功能能否正确处理API返回的数据格式
"""

import sys
import os

# 添加src目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from services.course_service import CourseService
from cache.global_data_manager import global_data_manager
from database.models import DatabaseManager

def test_category_data_format():
    """测试分类数据格式处理"""
    print("🧪 测试分类数据格式处理...")
    
    # 1. 加载数据到内存
    print("\n📦 步骤1: 加载数据到内存...")
    success = global_data_manager.load_all_data_once()
    if not success:
        print("❌ 数据加载失败，无法继续测试")
        return False
    
    # 2. 获取分类详情
    print("\n📂 步骤2: 获取分类详情...")
    db_manager = DatabaseManager("sqlite:///test.db")
    course_service = CourseService(db_manager)
    
    # 获取第一个分类进行测试
    category_list_result = global_data_manager.get_category_list(page=1, page_size=1)
    if not category_list_result['success'] or not category_list_result['data']:
        print("❌ 获取分类列表失败")
        return False
    
    first_category = category_list_result['data'][0]
    category_id = first_category['id']
    category_title = first_category['title']
    print(f"测试分类: ID={category_id}, 标题={category_title}")
    
    # 3. 获取分类详情
    result = course_service.get_category_detail(category_id)
    if not result['success']:
        print(f"❌ 获取分类详情失败: {result['message']}")
        return False
    
    category = result['data']
    print(f"✅ 获取分类详情成功")
    
    # 4. 检查数据格式
    print(f"\n🔍 步骤3: 检查分类数据格式...")
    print(f"分类数据字段: {list(category.keys())}")
    
    # 检查关键字段
    required_fields = ['id', 'title']
    optional_fields = ['description', 'price', 'order_index', 'series_id', 'seriesId']
    
    print(f"\n📋 字段检查:")
    for field in required_fields:
        if field in category:
            print(f"✅ {field}: {category[field]}")
        else:
            print(f"❌ 缺少必需字段: {field}")
            return False
    
    for field in optional_fields:
        if field in category:
            print(f"✅ {field}: {category[field]}")
        else:
            print(f"⚠️  可选字段缺失: {field}")
    
    # 5. 模拟分类编辑对话框的数据处理
    print(f"\n🔧 步骤4: 模拟分类编辑对话框数据处理...")
    
    try:
        # 模拟CategoryFormDialog.load_category_data()的逻辑
        title = category['title']
        description = category.get('description', '')
        
        # 处理价格字段
        price = category.get('price', 0)
        if price is None:
            price = 0
        price = float(price)
        
        # 处理order_index字段
        order_index = category.get('order_index', 0)
        if order_index is None:
            order_index = 0
        order_index = int(order_index)
        
        # 处理系列ID字段
        series_id = category.get('series_id') or category.get('seriesId')
        
        print(f"✅ 标题: {title}")
        print(f"✅ 描述: {description}")
        print(f"✅ 价格: {price}")
        print(f"✅ 排序索引: {order_index}")
        print(f"✅ 系列ID: {series_id}")
        
        print(f"\n🎉 分类编辑对话框数据处理成功！")
        return True
        
    except Exception as e:
        print(f"❌ 分类编辑对话框数据处理失败: {e}")
        return False

def test_multiple_categories_data_format():
    """测试多个分类的数据格式处理"""
    print("\n🔄 测试多个分类的数据格式处理...")
    
    db_manager = DatabaseManager("sqlite:///test.db")
    course_service = CourseService(db_manager)
    
    # 获取分类列表
    category_list_result = global_data_manager.get_category_list(page=1, page_size=5)
    if not category_list_result['success']:
        print("❌ 获取分类列表失败")
        return False
    
    categories = category_list_result['data']
    print(f"找到 {len(categories)} 个分类进行测试")
    
    success_count = 0
    
    for category_info in categories:
        category_id = category_info['id']
        category_title = category_info['title']
        
        try:
            result = course_service.get_category_detail(category_id)
            if result['success']:
                category = result['data']
                
                # 模拟数据处理
                title = category['title']
                price = float(category.get('price', 0) or 0)
                order_index = int(category.get('order_index', 0) or 0)
                series_id = category.get('series_id') or category.get('seriesId')
                
                print(f"✅ 分类 {category_id} ({category_title}) 数据处理成功")
                success_count += 1
            else:
                print(f"❌ 分类 {category_id} ({category_title}) 详情获取失败: {result['message']}")
        except Exception as e:
            print(f"❌ 分类 {category_id} ({category_title}) 数据处理失败: {e}")
    
    print(f"\n📊 测试结果: {success_count}/{len(categories)} 个分类数据处理成功")
    return success_count == len(categories)

if __name__ == "__main__":
    print("=" * 60)
    print("分类编辑order_index字段问题修正验证测试")
    print("=" * 60)
    
    try:
        # 测试分类数据格式处理
        format_success = test_category_data_format()
        
        if format_success:
            # 测试多个分类
            multiple_success = test_multiple_categories_data_format()
            
            if multiple_success:
                print("\n" + "=" * 60)
                print("🎉 所有测试通过！分类编辑order_index字段问题已修正。")
                print("现在点击分类编辑按钮应该不会再提示'order_index'错误了。")
                print("=" * 60)
            else:
                print("\n⚠️ 部分分类测试失败，可能存在数据问题")
        else:
            print("\n❌ 分类数据格式处理测试失败")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
