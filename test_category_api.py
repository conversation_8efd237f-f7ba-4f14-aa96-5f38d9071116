#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试分类API
"""

import requests
import json

def test_category_api():
    """测试分类API"""
    base_url = "http://localhost:8000"
    
    print("🔍 测试分类API...")
    
    # 1. 测试GET /api/categories
    print("\n1. 测试GET /api/categories")
    try:
        response = requests.get(f"{base_url}/api/categories", timeout=5)
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ 获取成功，分类数量: {len(data)}")
        else:
            print(f"   ❌ 获取失败: {response.text}")
    except Exception as e:
        print(f"   ❌ 请求异常: {e}")
    
    # 2. 测试POST /api/categories
    print("\n2. 测试POST /api/categories")
    test_data = {
        'title': '测试分类API',
        'seriesId': 'love-guide-series',
        'price': 99.99,
        'description': '测试分类创建',
        'order_index': 1,
        'isFree': False
    }
    
    try:
        response = requests.post(
            f"{base_url}/api/categories", 
            json=test_data,
            headers={'Content-Type': 'application/json'},
            timeout=5
        )
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200 or response.status_code == 201:
            print(f"   ✅ 创建成功: {response.json()}")
        else:
            print(f"   ❌ 创建失败: {response.text}")
    except Exception as e:
        print(f"   ❌ 请求异常: {e}")
    
    # 3. 测试其他可能的端点
    print("\n3. 测试其他可能的端点")
    
    endpoints_to_test = [
        "POST /api/admin/categories",
        "POST /api/categories/create",
        "PUT /api/categories",
    ]
    
    for endpoint in endpoints_to_test:
        method, path = endpoint.split(' ', 1)
        print(f"\n   测试 {endpoint}")
        try:
            if method == "POST":
                response = requests.post(
                    f"{base_url}{path}", 
                    json=test_data,
                    headers={'Content-Type': 'application/json'},
                    timeout=5
                )
            elif method == "PUT":
                response = requests.put(
                    f"{base_url}{path}", 
                    json=test_data,
                    headers={'Content-Type': 'application/json'},
                    timeout=5
                )
            
            print(f"      状态码: {response.status_code}")
            if response.status_code == 200 or response.status_code == 201:
                print(f"      ✅ 成功: {response.json()}")
            else:
                print(f"      ❌ 失败: {response.text}")
        except Exception as e:
            print(f"      ❌ 异常: {e}")

def test_server_status():
    """测试服务器状态"""
    base_url = "http://localhost:8000"
    
    print("🔍 测试服务器状态...")
    
    try:
        response = requests.get(f"{base_url}/", timeout=5)
        print(f"✅ 服务器运行正常，状态码: {response.status_code}")
        return True
    except Exception as e:
        print(f"❌ 服务器连接失败: {e}")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("分类API测试")
    print("=" * 60)
    
    # 先测试服务器状态
    if test_server_status():
        test_category_api()
    else:
        print("\n⚠️ 服务器未运行，请先启动服务器")
        print("启动命令: cd mock_server && python -m uvicorn src.main:app --reload --host 0.0.0.0 --port 8000")
