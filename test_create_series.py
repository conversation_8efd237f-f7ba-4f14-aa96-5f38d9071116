#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试创建系列功能
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_create_series():
    """测试创建系列功能"""
    print("🔍 测试创建系列功能...")
    
    try:
        from database.models import DatabaseManager
        from services.course_service import CourseService
        from utils.config import Config
        
        # 创建数据库连接
        config = Config()
        db_config = config.get_database_config()
        connection_string = f"mysql+pymysql://{db_config['user']}:{db_config['password']}@{db_config['host']}:{db_config['port']}/{db_config['database']}?charset={db_config['charset']}"
        db_manager = DatabaseManager(connection_string)
        
        # 创建课程服务
        course_service = CourseService(db_manager, use_api=False, use_cache=True)
        
        # 测试数据
        test_series_data = {
            'title': '测试系列：自动生成ID',
            'description': '这是一个测试系列，用于验证ID自动生成功能',
            'price': 99.99,
            'is_published': False
        }
        
        print(f"📝 创建测试系列: {test_series_data['title']}")
        
        # 创建系列
        result = course_service.create_series(test_series_data)
        
        if result['success']:
            print(f"✅ 系列创建成功!")
            print(f"   ID: {result['data']['id']}")
            print(f"   标题: {result['data']['title']}")
            print(f"   价格: ¥{result['data']['price']}")
            
            # 验证系列是否真的创建了
            series_id = result['data']['id']
            verify_result = course_service.get_series_detail(series_id)
            
            if verify_result['success']:
                print(f"✅ 系列验证成功: {verify_result['data']['title']}")
                
                # 清理测试数据
                delete_result = course_service.delete_series(series_id)
                if delete_result['success']:
                    print(f"🗑️ 测试数据已清理")
                
                return True
            else:
                print(f"❌ 系列验证失败: {verify_result['message']}")
                return False
        else:
            print(f"❌ 系列创建失败: {result['message']}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("创建系列功能测试")
    print("=" * 60)
    
    success = test_create_series()
    
    print("\n" + "=" * 60)
    print("测试结果")
    print("=" * 60)
    
    if success:
        print("✅ 创建系列功能正常")
        print("\n🎉 修复成果：")
        print("- 🆔 自动生成唯一ID")
        print("- 💾 数据库保存成功")
        print("- 🔍 数据验证通过")
        print("- 🗑️ 测试数据清理完成")
        print("\n现在可以正常创建新系列了！")
    else:
        print("❌ 创建系列功能仍有问题")

if __name__ == "__main__":
    main()
