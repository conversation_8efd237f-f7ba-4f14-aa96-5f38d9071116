#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试更新问题
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_database_update():
    """测试数据库更新"""
    print("🔍 测试数据库更新...")
    
    try:
        from database.connection import DatabaseConnection
        from database.models import DatabaseManager
        from database.dao import SeriesDAO
        from utils.config import Config

        # 创建数据库连接
        config = Config()
        db_config = config.get_database_config()

        # 构造连接字符串
        connection_string = f"mysql+pymysql://{db_config['user']}:{db_config['password']}@{db_config['host']}:{db_config['port']}/{db_config['database']}?charset={db_config['charset']}"
        db_manager = DatabaseManager(connection_string)
        
        # 获取会话
        session = db_manager.get_session()
        series_dao = SeriesDAO(session)
        
        # 查找道系列
        series_list, total = series_dao.get_all()
        print(f"📊 数据库中的系列数量: {len(series_list)}")

        for series in series_list:
            print(f"系列: {series.id} - {series.title}")
            if "道" in series.title:
                print(f"🎯 找到道系列: {series.id} - {series.title}")
                
                # 尝试更新
                old_title = series.title
                new_title = f"{old_title}（测试更新）"
                
                print(f"🔄 尝试更新标题: {old_title} -> {new_title}")
                
                updated_series = series_dao.update(series.id, {'title': new_title})
                session.commit()
                
                if updated_series:
                    print(f"✅ 数据库更新成功: {updated_series.title}")
                    
                    # 恢复原标题
                    series_dao.update(series.id, {'title': old_title})
                    session.commit()
                    print(f"🔄 已恢复原标题: {old_title}")
                    
                    return True
                else:
                    print(f"❌ 数据库更新失败")
                    return False
                    
        print("❌ 没有找到道系列")
        return False
        
    except Exception as e:
        print(f"❌ 数据库更新测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        if 'session' in locals():
            session.close()

def test_memory_cache():
    """测试内存缓存"""
    print("\n🔍 测试内存缓存...")
    
    try:
        from cache.global_data_manager import global_data_manager
        
        # 检查内存数据是否加载
        if not global_data_manager.is_data_loaded():
            print("📦 内存数据未加载，尝试加载...")
            global_data_manager.load_all_data_once()
        
        # 获取系列数据
        result = global_data_manager.get_series_list(page=1, page_size=100)
        if result.get('success'):
            series_list = result['data']
            print(f"📊 内存中的系列数量: {len(series_list)}")
            
            for series in series_list:
                if "道" in series.get('title', ''):
                    print(f"🎯 找到道系列: {series['id']} - {series['title']}")
                    
                    # 尝试更新内存
                    old_title = series['title']
                    new_title = f"{old_title}（内存测试更新）"
                    
                    print(f"🔄 尝试更新内存标题: {old_title} -> {new_title}")
                    
                    global_data_manager.update_series_in_memory(series['id'], {'title': new_title})
                    
                    # 验证更新
                    updated_result = global_data_manager.get_series_list(page=1, page_size=100)
                    if updated_result.get('success'):
                        updated_series_list = updated_result['data']
                        for updated_series in updated_series_list:
                            if updated_series['id'] == series['id']:
                                if updated_series['title'] == new_title:
                                    print(f"✅ 内存更新成功: {updated_series['title']}")
                                    
                                    # 恢复原标题
                                    global_data_manager.update_series_in_memory(series['id'], {'title': old_title})
                                    print(f"🔄 已恢复原标题: {old_title}")
                                    
                                    return True
                                else:
                                    print(f"❌ 内存更新失败，标题仍为: {updated_series['title']}")
                                    return False
                    
                    break
            
            print("❌ 没有找到道系列")
            return False
        else:
            print(f"❌ 获取内存数据失败: {result}")
            return False
            
    except Exception as e:
        print(f"❌ 内存缓存测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_server_sync():
    """测试服务器同步"""
    print("\n🔍 测试服务器同步...")
    
    try:
        from sync.data_sync_manager import DataSyncManager
        
        # 创建同步管理器
        sync_manager = DataSyncManager(
            local_db_config={
                'host': 'localhost',
                'user': 'mike',
                'password': 'dyj217',
                'database': 'shuimu_course',
                'charset': 'utf8mb4'
            },
            api_base_url='http://localhost:8000'
        )
        
        # 测试连接
        connection_result = sync_manager.test_connection()
        if not connection_result['success']:
            print(f"❌ 服务器连接失败: {connection_result['message']}")
            return False
        
        print(f"✅ 服务器连接成功")
        
        # 尝试同步一个测试数据
        test_data = {
            'title': '道：恋爱宝典（同步测试）'
        }
        
        sync_result = sync_manager.sync_to_server('series', 'love-guide-series', test_data)
        
        if sync_result['success']:
            print(f"✅ 服务器同步成功: {sync_result['message']}")
            return True
        else:
            print(f"❌ 服务器同步失败: {sync_result['message']}")
            return False
            
    except Exception as e:
        print(f"❌ 服务器同步测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("调试更新问题")
    print("=" * 60)
    
    test_results = []
    
    # 测试数据库更新
    test_results.append(("数据库更新", test_database_update()))
    
    # 测试内存缓存
    test_results.append(("内存缓存", test_memory_cache()))
    
    # 测试服务器同步
    test_results.append(("服务器同步", test_server_sync()))
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("调试结果")
    print("=" * 60)
    
    for test_name, result in test_results:
        status = "✅ 正常" if result else "❌ 异常"
        print(f"{test_name}: {status}")
    
    failed_tests = [name for name, result in test_results if not result]
    if failed_tests:
        print(f"\n⚠️ 发现问题的环节: {', '.join(failed_tests)}")
        print("这些环节需要修复才能让更新功能正常工作")
    else:
        print("\n✅ 所有环节都正常，问题可能在其他地方")

if __name__ == "__main__":
    main()
